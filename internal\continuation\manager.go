package continuation

import (
	"faq-system/internal/logger"
	"faq-system/internal/nlp"
	"strings"
	"time"
)

// ConversationContext 对话上下文
type ConversationContext struct {
	UserID           string                 `json:"user_id"`
	LastQuery        string                 `json:"last_query"`
	LastAnswer       string                 `json:"last_answer"`
	LastIntent       string                 `json:"last_intent"`
	LastSource       string                 `json:"last_source"`
	LastTimestamp    time.Time              `json:"last_timestamp"`
	ContinuationData map[string]interface{} `json:"continuation_data"`
	CanContinue      bool                   `json:"can_continue"`
}

// ContinuationResult 续接结果
type ContinuationResult struct {
	Success         bool                   `json:"success"`
	ContinuedAnswer string                 `json:"continued_answer"`
	Source          string                 `json:"source"`
	Intent          string                 `json:"intent"`
	Confidence      float64                `json:"confidence"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// Manager 续接管理器
type Manager struct {
	contexts     map[string]*ConversationContext
	nlpProcessor *nlp.IntegratedProcessor
	maxContexts  int
}

// NewManager 创建续接管理器
func NewManager(nlpProcessor *nlp.IntegratedProcessor) *Manager {
	return &Manager{
		contexts:     make(map[string]*ConversationContext),
		nlpProcessor: nlpProcessor,
		maxContexts:  100, // 最多保存100个用户的上下文
	}
}

// UpdateContext 更新对话上下文
func (m *Manager) UpdateContext(userID, query, answer, intent, source string) {
	// 清理过期的上下文
	m.cleanupOldContexts()

	// 判断是否可以续接
	canContinue := m.isAnswerContinuable(answer, intent)

	context := &ConversationContext{
		UserID:           userID,
		LastQuery:        query,
		LastAnswer:       answer,
		LastIntent:       intent,
		LastSource:       source,
		LastTimestamp:    time.Now(),
		ContinuationData: make(map[string]interface{}),
		CanContinue:      canContinue,
	}

	// 如果答案可以续接，提取续接数据
	if canContinue {
		context.ContinuationData = m.extractContinuationData(answer, intent)
	}

	m.contexts[userID] = context
	logger.Infof("🔄 更新用户 %s 的对话上下文，可续接: %v", userID, canContinue)
}

// HandleContinuation 处理续接请求
func (m *Manager) HandleContinuation(userID, continuationQuery string) *ContinuationResult {
	context, exists := m.contexts[userID]
	if !exists {
		logger.Infof("❌ 用户 %s 没有可续接的上下文", userID)
		return &ContinuationResult{
			Success: false,
			Metadata: map[string]interface{}{
				"reason": "no_context",
			},
		}
	}

	// 检查上下文是否过期（超过10分钟）
	if time.Since(context.LastTimestamp) > 10*time.Minute {
		logger.Infof("❌ 用户 %s 的上下文已过期", userID)
		delete(m.contexts, userID)
		return &ContinuationResult{
			Success: false,
			Metadata: map[string]interface{}{
				"reason": "context_expired",
			},
		}
	}

	if !context.CanContinue {
		logger.Infof("❌ 用户 %s 的上一个回答不支持续接", userID)
		return &ContinuationResult{
			Success: false,
			Metadata: map[string]interface{}{
				"reason": "not_continuable",
			},
		}
	}

	// 生成续接内容
	continuedAnswer := m.generateContinuation(context, continuationQuery)
	if continuedAnswer == "" {
		logger.Infof("❌ 无法为用户 %s 生成续接内容", userID)
		return &ContinuationResult{
			Success: false,
			Metadata: map[string]interface{}{
				"reason": "no_continuation_generated",
			},
		}
	}

	logger.Infof("✅ 为用户 %s 生成续接内容，长度: %d", userID, len(continuedAnswer))

	return &ContinuationResult{
		Success:         true,
		ContinuedAnswer: continuedAnswer,
		Source:          context.LastSource + " (续接)",
		Intent:          context.LastIntent,
		Confidence:      0.8,
		Metadata: map[string]interface{}{
			"original_query":  context.LastQuery,
			"original_answer": context.LastAnswer,
			"continuation_type": m.getContinuationType(continuationQuery),
		},
	}
}

// isAnswerContinuable 判断答案是否可以续接
func (m *Manager) isAnswerContinuable(answer, intent string) bool {
	// 技术问题通常可以续接
	if intent == "technical_question" || intent == "learned_knowledge" {
		return true
	}

	// 长答案通常可以续接
	if len(answer) > 100 {
		return true
	}

	// 包含列表、步骤等结构化内容的答案可以续接
	continuablePatterns := []string{
		"包括", "例如", "比如", "首先", "其次", "另外", "此外",
		"步骤", "方法", "方式", "特点", "优势", "缺点",
		"•", "1.", "2.", "3.", "-", "①", "②", "③",
	}

	for _, pattern := range continuablePatterns {
		if strings.Contains(answer, pattern) {
			return true
		}
	}

	return false
}

// extractContinuationData 提取续接数据
func (m *Manager) extractContinuationData(answer, intent string) map[string]interface{} {
	data := make(map[string]interface{})

	// 提取关键词
	if m.nlpProcessor != nil && m.nlpProcessor.IsInitialized() {
		result := m.nlpProcessor.ProcessText(answer)
		if result != nil && len(result.Keywords) > 0 {
			data["keywords"] = result.Keywords
		}
	}

	// 分析答案结构
	data["answer_length"] = len(answer)
	data["has_list"] = strings.Contains(answer, "•") || strings.Contains(answer, "1.") || strings.Contains(answer, "-")
	data["has_steps"] = strings.Contains(answer, "步骤") || strings.Contains(answer, "方法")
	data["intent"] = intent

	return data
}

// generateContinuation 生成续接内容
func (m *Manager) generateContinuation(context *ConversationContext, continuationQuery string) string {
	continuationType := m.getContinuationType(continuationQuery)
	
	switch context.LastIntent {
	case "technical_question", "learned_knowledge":
		return m.generateTechnicalContinuation(context, continuationType)
	case "algorithm_request":
		return m.generateAlgorithmContinuation(context, continuationType)
	default:
		return m.generateGeneralContinuation(context, continuationType)
	}
}

// getContinuationType 获取续接类型
func (m *Manager) getContinuationType(query string) string {
	query = strings.ToLower(query)
	
	if strings.Contains(query, "详细") || strings.Contains(query, "具体") {
		return "detailed"
	}
	if strings.Contains(query, "例子") || strings.Contains(query, "示例") {
		return "example"
	}
	if strings.Contains(query, "步骤") || strings.Contains(query, "方法") {
		return "steps"
	}
	if strings.Contains(query, "还有") || strings.Contains(query, "其他") {
		return "more"
	}
	
	return "general"
}

// generateTechnicalContinuation 生成技术续接内容
func (m *Manager) generateTechnicalContinuation(context *ConversationContext, continuationType string) string {
	baseAnswer := context.LastAnswer
	
	switch continuationType {
	case "detailed":
		return m.generateDetailedExplanation(baseAnswer)
	case "example":
		return m.generateExamples(baseAnswer)
	case "steps":
		return m.generateSteps(baseAnswer)
	case "more":
		return m.generateMoreInfo(baseAnswer)
	default:
		return m.generateGeneralTechnicalContinuation(baseAnswer)
	}
}

// generateDetailedExplanation 生成详细解释
func (m *Manager) generateDetailedExplanation(baseAnswer string) string {
	return "让我为您详细解释一下：\n\n" +
		"📋 **深入分析**：\n" +
		"• 从技术原理来看，这涉及到多个层面的考虑\n" +
		"• 在实际应用中，需要注意性能和兼容性问题\n" +
		"• 最佳实践建议结合具体场景来选择合适的方案\n\n" +
		"💡 **关键要点**：\n" +
		"• 理解底层机制有助于更好地应用这个技术\n" +
		"• 注意边界条件和异常处理\n" +
		"• 考虑扩展性和维护性"
}

// generateExamples 生成示例
func (m *Manager) generateExamples(baseAnswer string) string {
	return "让我给您举几个具体的例子：\n\n" +
		"🔍 **示例1**：\n" +
		"在实际项目中，这种情况经常出现...\n\n" +
		"🔍 **示例2**：\n" +
		"另一个常见的应用场景是...\n\n" +
		"🔍 **示例3**：\n" +
		"在特殊情况下，我们可能需要..."
}

// generateSteps 生成步骤
func (m *Manager) generateSteps(baseAnswer string) string {
	return "让我为您详细说明具体步骤：\n\n" +
		"📝 **第一步**：准备工作\n" +
		"• 确认环境配置\n" +
		"• 检查依赖项\n\n" +
		"📝 **第二步**：核心实现\n" +
		"• 编写主要逻辑\n" +
		"• 处理边界情况\n\n" +
		"📝 **第三步**：测试验证\n" +
		"• 单元测试\n" +
		"• 集成测试"
}

// generateMoreInfo 生成更多信息
func (m *Manager) generateMoreInfo(baseAnswer string) string {
	return "还有一些重要的补充信息：\n\n" +
		"🔧 **相关技术**：\n" +
		"• 与此相关的其他技术栈\n" +
		"• 可以配合使用的工具\n\n" +
		"⚠️ **注意事项**：\n" +
		"• 常见的陷阱和误区\n" +
		"• 性能优化建议\n\n" +
		"📚 **进一步学习**：\n" +
		"• 推荐的学习资源\n" +
		"• 相关的最佳实践"
}

// generateGeneralTechnicalContinuation 生成通用技术续接
func (m *Manager) generateGeneralTechnicalContinuation(baseAnswer string) string {
	return "让我继续为您补充一些相关信息：\n\n" +
		"💡 **补充说明**：\n" +
		"基于刚才的回答，还有几个重要的方面需要考虑...\n\n" +
		"🎯 **实践建议**：\n" +
		"在实际应用中，建议您注意以下几点..."
}

// generateAlgorithmContinuation 生成算法续接内容
func (m *Manager) generateAlgorithmContinuation(context *ConversationContext, continuationType string) string {
	return "关于算法的进一步说明：\n\n" +
		"📊 **复杂度分析**：\n" +
		"• 时间复杂度考虑\n" +
		"• 空间复杂度优化\n\n" +
		"🔄 **算法优化**：\n" +
		"• 可能的改进方案\n" +
		"• 适用场景分析"
}

// generateGeneralContinuation 生成通用续接内容
func (m *Manager) generateGeneralContinuation(context *ConversationContext, continuationType string) string {
	return "让我继续为您说明：\n\n" +
		"基于前面的内容，还有一些相关的信息可以分享给您..."
}

// cleanupOldContexts 清理过期的上下文
func (m *Manager) cleanupOldContexts() {
	if len(m.contexts) <= m.maxContexts {
		return
	}

	// 删除最旧的上下文
	oldestTime := time.Now()
	oldestUserID := ""
	
	for userID, context := range m.contexts {
		if context.LastTimestamp.Before(oldestTime) {
			oldestTime = context.LastTimestamp
			oldestUserID = userID
		}
	}
	
	if oldestUserID != "" {
		delete(m.contexts, oldestUserID)
		logger.Infof("🧹 清理过期上下文: %s", oldestUserID)
	}
}

// GetContext 获取用户上下文
func (m *Manager) GetContext(userID string) *ConversationContext {
	return m.contexts[userID]
}

// HasContinuableContext 检查用户是否有可续接的上下文
func (m *Manager) HasContinuableContext(userID string) bool {
	context, exists := m.contexts[userID]
	if !exists {
		return false
	}
	
	// 检查是否过期
	if time.Since(context.LastTimestamp) > 10*time.Minute {
		delete(m.contexts, userID)
		return false
	}
	
	return context.CanContinue
}

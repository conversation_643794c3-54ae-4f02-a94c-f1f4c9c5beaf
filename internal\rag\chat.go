package rag

import (
	"fmt"
	"regexp"
	"strings"
	"time"

	"faq-system/internal/algorithm"
	"faq-system/internal/algorithms"
	"faq-system/internal/answer"
	"faq-system/internal/embedding"
	"faq-system/internal/learning"
	"faq-system/internal/logger"
	"faq-system/internal/mysql"
	"faq-system/internal/nlp"
	"faq-system/internal/recommendation"
	"faq-system/internal/semantic"
	"faq-system/internal/smartmatch"
	"faq-system/internal/vectorstore"
)

// ChatSystem RAG聊天系统
type ChatSystem struct {
	intentClassifier    *IntentClassifier
	contextManager      *ContextManager
	vectorStore         *vectorstore.VectorStore
	embedClient         *embedding.Client
	faqs                []mysql.FAQ
	smartMatcher        *smartmatch.SmartMatcher
	semanticMatcher     *semantic.SmartMatcher
	answerGenerator     *answer.Generator
	algorithmRecognizer *algorithm.Recognizer
	algorithmExecutor   *algorithms.AlgorithmExecutor
	learningEngine      *learning.LearningEngine
	evolutionMatcher    *learning.SmartMatcher     // 新增：智能进化匹配器
	knowledgeLearner    *learning.KnowledgeLearner // 新增：知识学习器

	// 🧠 新增：强大的NLP处理器
	nlpProcessor        *nlp.IntegratedProcessor
	conversationManager *nlp.ConversationManager
	knowledgeGraph      *nlp.KnowledgeGraph
	adaptiveLearning    *nlp.AdaptiveLearningSystem

	// 🔗 新增：推荐服务
	recommendationService *recommendation.Service
}

// IntentClassifier 意图分类器
type IntentClassifier struct {
	patterns map[string][]string
}

// ContextManager 上下文管理器
type ContextManager struct {
	conversations map[string]*Conversation
	maxHistory    int
}

// Conversation 对话上下文
type Conversation struct {
	UserID    string
	SessionID string
	Messages  []Message
	History   []string
	Intent    string
	LastTime  time.Time

	// 🧠 NLP增强字段
	LastNLPResult *nlp.IntegratedResult
	LastSentiment string
	LastEntities  []nlp.ExtractedEntity
}

// Message 消息
type Message struct {
	Role      string
	Content   string
	Timestamp time.Time
	Intent    string
	Sources   []string
}

// Response RAG响应
type Response struct {
	Answer     string
	Source     string
	Intent     string
	Confidence float32
	Duration   time.Duration
}

// NewChatSystem 创建RAG聊天系统
func NewChatSystem(vectorStore *vectorstore.VectorStore, embedClient *embedding.Client, faqs []mysql.FAQ) *ChatSystem {
	logger.Info("🧠 初始化增强RAG聊天系统...")

	// 初始化NLP处理器
	nlpProcessor := nlp.NewIntegratedProcessor()

	logger.Info("✅ 增强RAG聊天系统初始化完成")

	return &ChatSystem{
		intentClassifier:    NewIntentClassifier(),
		contextManager:      NewContextManager(),
		vectorStore:         vectorStore,
		embedClient:         embedClient,
		faqs:                faqs,
		smartMatcher:        smartmatch.NewSmartMatcher(),
		semanticMatcher:     semantic.NewSmartMatcher(faqs),
		answerGenerator:     answer.NewAnswerGenerator(faqs),
		algorithmRecognizer: algorithm.NewRecognizer(),
		algorithmExecutor:   algorithms.NewAlgorithmExecutor(),
		learningEngine:      nil, // 将在SetLearningEngine中设置

		// 🧠 强大的NLP组件
		nlpProcessor:        nlpProcessor,
		conversationManager: nil, // 将在后续初始化
		knowledgeGraph:      nil, // 将在后续初始化
		adaptiveLearning:    nil, // 将在后续初始化
	}
}

// SetLearningEngine 设置学习引擎
func (cs *ChatSystem) SetLearningEngine(engine *learning.LearningEngine) {
	cs.learningEngine = engine
}

// SetEvolutionMatcher 设置智能进化匹配器
func (cs *ChatSystem) SetEvolutionMatcher(matcher *learning.SmartMatcher) {
	cs.evolutionMatcher = matcher
}

// SetKnowledgeLearner 设置知识学习器
func (cs *ChatSystem) SetKnowledgeLearner(learner *learning.KnowledgeLearner) {
	cs.knowledgeLearner = learner

	// 初始化推荐服务
	if learner != nil {
		cs.recommendationService = recommendation.NewService(learner, nil)
		logger.Info("🔗 推荐服务已初始化")
	}
}

// getPreviousQuery 获取上一个查询
func (cs *ChatSystem) getPreviousQuery(context *Conversation) string {
	if len(context.History) > 0 {
		return context.History[len(context.History)-1]
	}
	return ""
}

// ProcessKnowledgeInput 处理用户提供的知识输入
func (cs *ChatSystem) ProcessKnowledgeInput(userID, query, userResponse string) (*Response, error) {
	if cs.knowledgeLearner == nil {
		return &Response{
			Answer:     "抱歉，知识学习功能暂时不可用。",
			Source:     "系统",
			Intent:     "system_message",
			Confidence: 0.0,
		}, nil
	}

	// 尝试从用户输入中学习知识
	err := cs.knowledgeLearner.LearnFromUserInput(userID, query, userResponse, "")
	if err != nil {
		logger.Errorf("知识学习失败: %v", err)
		return &Response{
			Answer:     "抱歉，我在学习您提供的知识时遇到了问题。",
			Source:     "系统",
			Intent:     "system_message",
			Confidence: 0.0,
		}, nil
	}

	// 检查是否成功学习到了知识
	pendingKnowledge, err := cs.knowledgeLearner.GetPendingKnowledge(1)
	if err == nil && len(pendingKnowledge) > 0 {
		// 自动批准高置信度的知识
		latestKnowledge := pendingKnowledge[0]
		if latestKnowledge.Confidence > 0.8 {
			cs.knowledgeLearner.ApproveKnowledge(latestKnowledge.ID)
			return &Response{
				Answer: fmt.Sprintf("✅ 谢谢您的教学！我已经学会了：\n\n**问题**: %s\n**答案**: %s\n\n这个知识已经被添加到我的知识库中，我可以用它来回答类似的问题。",
					latestKnowledge.Question, latestKnowledge.Answer),
				Source:     "知识学习系统",
				Intent:     "knowledge_learned",
				Confidence: 1.0,
			}, nil
		} else {
			return &Response{
				Answer: fmt.Sprintf("🧠 谢谢您的输入！我学到了一些新知识：\n\n**问题**: %s\n**答案**: %s\n\n这个知识正在审核中，审核通过后我就可以使用它来回答问题了。",
					latestKnowledge.Question, latestKnowledge.Answer),
				Source:     "知识学习系统",
				Intent:     "knowledge_pending",
				Confidence: 0.8,
			}, nil
		}
	}

	return &Response{
		Answer:     "🤔 我尝试从您的输入中学习，但没有识别出明确的知识模式。您可以尝试用\"X是Y\"的格式来教我新知识。",
		Source:     "知识学习系统",
		Intent:     "learning_guidance",
		Confidence: 0.5,
	}, nil
}

// NewIntentClassifier 创建意图分类器
func NewIntentClassifier() *IntentClassifier {
	patterns := map[string][]string{
		"greeting": {
			"你好", "hello", "hi", "嗨", "您好", "早上好", "下午好", "晚上好",
			"大家好", "各位好", "小伙伴们好", "朋友们好",
		},
		"identity_inquiry": {
			"你是谁", "你是什么", "你叫什么", "介绍一下自己", "你的身份", "你是",
		},
		"chat_request": {
			"聊天", "聊", "对话", "交流", "谈话", "陪我聊", "和我聊", "能聊", "可以聊",
			"chat", "talk", "conversation", "能和我聊天吗", "可以和我聊天吗",
			"我们聊聊", "聊聊天", "随便聊聊", "闲聊", "聊天吧",
		},
		"technical_question": {
			"什么是", "如何", "怎么", "为什么", "原理", "实现", "配置", "安装", "部署",
			"localai", "mysql", "数据库", "向量", "embedding", "go语言", "golang",
			"搜索", "系统", "架构", "开发", "编程", "技术", "代码", "框架", "工具",
		},
		"algorithm_request": {
			// 数学计算相关
			"计算", "算", "求", "等于", "多少", "结果", "答案", "数学", "运算", "求解", "解方程",
			"加", "减", "乘", "除", "加法", "减法", "乘法", "除法",
			"平方", "开方", "平方根", "立方", "次方", "幂", "指数", "对数", "三角函数",
			"sin", "cos", "tan", "log", "ln", "sqrt", "abs", "max", "min",
			"绝对值", "最大值", "最小值", "表达式", "公式", "方程",

			// 算法相关
			"算法", "排序", "搜索", "查找", "冒泡排序", "快速排序", "归并排序",
			"二分查找", "线性搜索", "动态规划", "图算法",

			// 统计相关
			"统计", "平均数", "中位数", "众数", "方差", "标准差", "概率",

			// 数论相关
			"质数", "素数", "最大公约数", "最小公倍数", "斐波那契",
		},
		"follow_up": {
			"继续", "详细说明", "举例", "更多信息", "具体怎么", "还有吗", "然后呢",
		},
		"thanks": {
			"谢谢", "感谢", "thank you", "thanks", "多谢",
		},
	}

	return &IntentClassifier{patterns: patterns}
}

// NewContextManager 创建上下文管理器
func NewContextManager() *ContextManager {
	return &ContextManager{
		conversations: make(map[string]*Conversation),
		maxHistory:    10,
	}
}

// ProcessQuery 处理查询
func (cs *ChatSystem) ProcessQuery(userID, query string) (*Response, error) {
	start := time.Now()

	// 🧠 1. 使用NLP处理器进行智能分析
	var intent string
	var nlpResult *nlp.IntegratedResult

	// 🧠 调试信息：检查NLP处理器状态
	logger.Info("🔍 NLP处理器状态检查: processor=%v, initialized=%v",
		cs.nlpProcessor != nil,
		cs.nlpProcessor != nil && cs.nlpProcessor.IsInitialized())

	if cs.nlpProcessor != nil && cs.nlpProcessor.IsInitialized() {
		// 使用强大的NLP处理器分析查询
		logger.Info("🧠 使用NLP处理器分析查询: %s", query)
		nlpResult = cs.nlpProcessor.ProcessText(query)
		if nlpResult.Intent != nil {
			intent = nlpResult.Intent.Intent
			logger.Info("🧠 NLP智能意图识别: %s (置信度: %.2f)", intent, nlpResult.Intent.Confidence)
		} else {
			intent = "general"
			logger.Info("⚠️ NLP处理器未返回意图，使用默认: %s", intent)
		}
	} else {
		// 回退到简单意图分类器
		intent = cs.intentClassifier.ClassifyIntent(query)
		logger.Info("📝 简单意图识别: %s", intent)

		// 🧠 调试：检查数学表达式检测
		hasMath := cs.intentClassifier.ContainsMathExpression(query)
		logger.Info("🧮 数学表达式检测: query=%s, hasMath=%v, intent=%s", query, hasMath, intent)

		// 🧠 增强简单意图分类器，特别处理聊天请求
		if intent == "general" {
			// 检查是否是聊天请求
			queryLower := strings.ToLower(query)
			chatKeywords := []string{"聊天", "聊", "对话", "交流", "谈话", "陪我聊", "和我聊", "能聊", "可以聊", "能和我聊天吗", "可以和我聊天吗"}

			for _, keyword := range chatKeywords {
				if strings.Contains(queryLower, keyword) {
					intent = "chat_request"
					logger.Info("🧠 智能检测到聊天请求，修正意图: %s", intent)
					break
				}
			}
		}
	}

	// 2. 获取对话上下文
	context := cs.contextManager.GetContext(userID)

	// 🧠 3. 智能对话处理
	if nlpResult != nil {
		// 记录NLP分析结果到上下文
		context.LastNLPResult = nlpResult

		// 如果检测到情感变化，调整响应策略
		if nlpResult.SentimentDetail != nil {
			context.LastSentiment = nlpResult.SentimentDetail.Label
			logger.Info("😊 情感分析: %s (强度: %.2f)", nlpResult.SentimentDetail.Label, nlpResult.SentimentDetail.Score)
		}

		// 如果提取到实体，记录到上下文
		if len(nlpResult.ExtractedEntities) > 0 {
			context.LastEntities = nlpResult.ExtractedEntities
			logger.Info("🏷️ 实体识别: %d 个实体", len(nlpResult.ExtractedEntities))
		}
	}

	// 3. 数据收集 - 记录查询
	var queryID int64
	var embedding []float32
	if cs.learningEngine != nil {
		// 生成查询向量（如果可能）
		if cs.embedClient != nil {
			if embeddingVec, err := cs.embedClient.EmbedText(query); err == nil {
				embedding = embeddingVec
			}
		}

		// 创建查询上下文
		queryContext := &learning.QueryContext{
			SessionID:       context.SessionID,
			UserID:          userID,
			PreviousQueries: context.History,
			Timestamp:       time.Now(),
		}

		// 记录查询
		if id, err := cs.learningEngine.GetCollector().RecordQuery(
			context.SessionID, userID, query, intent, "", embedding, queryContext); err == nil {
			queryID = id
		}
	}

	// 3. 根据意图选择处理策略
	var response *Response
	var err error

	// 🧠 智能意图处理增强
	switch intent {
	case "greeting":
		response = cs.handleGreeting(query, context)
	case "chat", "chat_request", "conversation_request":
		response = cs.handleChatRequest(query, context)
	case "technical_question":
		response, err = cs.handleTechnicalQuestion(query, context)
	case "algorithm_request":
		response, err = cs.handleAlgorithmRequest(query, context)
	case "identity_inquiry":
		response = cs.handleIdentityInquiry(query, context)
	case "question":
		// 🧠 智能问题处理 - 根据问题内容智能路由
		response = cs.handleIntelligentQuestion(query, context, nlpResult)
	case "follow_up":
		response, err = cs.handleFollowUp(query, context)
	case "thanks":
		response = cs.handleThanks(query, context)
	case "feedback":
		response = cs.handleFeedback(query, context)
	case "confirmation":
		response = cs.handleConfirmation(query, context)
	default:
		// 🧠 智能通用处理
		response, err = cs.handleIntelligentGeneral(query, context, nlpResult)
	}

	if err != nil {
		return nil, err
	}

	// 4. 数据收集 - 记录响应
	processingTime := int(time.Since(start).Milliseconds())
	if cs.learningEngine != nil && queryID > 0 {
		var faqID *int
		// 尝试从响应中提取FAQ ID（如果有的话）
		// 这里需要根据实际的Response结构来实现

		cs.learningEngine.GetCollector().RecordResponse(
			queryID, faqID, response.Answer, response.Source,
			response.Intent, &response.Confidence, processingTime)
	}

	// 5. 🚫 禁用自动学习功能 - 防止错误学习
	// 注释掉自动学习逻辑，避免在没有找到答案时错误地学习用户问题
	/*
		if cs.knowledgeLearner != nil {
			// 如果系统没有找到好的答案，可能用户会提供知识
			if response.Confidence < 0.5 {
				// 异步学习，不影响响应速度
				go func() {
					if err := cs.knowledgeLearner.LearnFromUserInput(userID, query, "", response.Answer); err != nil {
						logger.Warnf("知识学习失败: %v", err)
					}
				}()
			}
		}
	*/

	// 🔍 记录低置信度查询，供后续分析
	if response.Confidence < 0.5 {
		logger.Infof("📊 低置信度查询记录: %s (置信度: %.2f)", query, response.Confidence)
	}

	// 6. 更新对话上下文
	cs.contextManager.UpdateContext(userID, query, response.Answer, intent)

	response.Duration = time.Since(start)
	return response, nil
}

// ClassifyIntent 分类意图
func (ic *IntentClassifier) ClassifyIntent(query string) string {
	query = strings.ToLower(strings.TrimSpace(query))

	// 优先检查是否包含数学表达式特征
	if ic.containsMathExpression(query) {
		return "algorithm_request"
	}

	// 对于简单查询（单个技术词汇或过短查询），更谨慎处理
	isSimpleQuery := ic.isSimpleQuery(query)

	// 计算每个意图的匹配分数
	scores := make(map[string]float32)

	for intent, patterns := range ic.patterns {
		score := float32(0)
		for _, pattern := range patterns {
			if strings.Contains(query, pattern) {
				// 完全匹配得分更高
				if query == pattern {
					score += 2.0
				} else {
					score += 1.0
				}
			}
		}

		// 对简单查询进行特殊处理
		if isSimpleQuery && intent == "technical_question" {
			// 简单查询需要更强的技术问题特征才能被识别为技术问题
			if !ic.hasStrongTechnicalIndicators(query) {
				score *= 0.1 // 大幅降低简单查询的技术问题得分
			}
		}

		scores[intent] = score
	}

	// 对算法请求给予额外权重
	if scores["algorithm_request"] > 0 {
		scores["algorithm_request"] *= 1.2 // 增加20%权重
	}

	// 找到最高分的意图
	maxScore := float32(0)
	bestIntent := "general"

	for intent, score := range scores {
		if score > maxScore {
			maxScore = score
			bestIntent = intent
		}
	}

	// 最后检查：如果是简单查询但被识别为技术问题，强制改为general
	if isSimpleQuery && bestIntent == "technical_question" && !ic.hasStrongTechnicalIndicators(query) {
		bestIntent = "general"
	}

	return bestIntent
}

// containsMathExpression 检查是否包含数学表达式特征
func (ic *IntentClassifier) containsMathExpression(query string) bool {
	query = strings.TrimSpace(query)

	// 检查是否是简单的数学表达式格式（如 1+1= 或 1+1=?）
	if ic.isSimpleMathFormat(query) {
		return true
	}

	// 检查是否包含数字和运算符的组合
	hasNumber := false
	hasOperator := false

	for _, char := range query {
		if char >= '0' && char <= '9' {
			hasNumber = true
		}
		if strings.ContainsRune("+-*/=×÷", char) {
			hasOperator = true
		}
	}

	// 如果同时包含数字和运算符，很可能是数学表达式
	if hasNumber && hasOperator {
		return true
	}

	// 检查是否包含数学函数
	mathFunctions := []string{"sin", "cos", "tan", "log", "ln", "sqrt", "abs", "exp", "pow"}
	for _, fn := range mathFunctions {
		if strings.Contains(query, fn) {
			return true
		}
	}

	// 检查是否包含明确的数学计算词汇
	mathWords := []string{"计算", "求", "等于", "多少", "结果"}
	for _, word := range mathWords {
		if strings.Contains(query, word) && hasNumber {
			return true
		}
	}

	return false
}

// ContainsMathExpression 公开的数学表达式检测方法（用于测试）
func (ic *IntentClassifier) ContainsMathExpression(query string) bool {
	return ic.containsMathExpression(query)
}

// isSimpleMathFormat 检查是否是简单的数学表达式格式
func (ic *IntentClassifier) isSimpleMathFormat(query string) bool {
	query = strings.ToLower(strings.TrimSpace(query))

	// 匹配 数字+运算符+数字+等号 的模式（如 1+1= 或 2*3=?）
	patterns := []string{
		`^\d+\s*[\+\-\*\/]\s*\d+\s*=\s*\??\s*$`,                    // 1+1= 或 1+1=?
		`^\d+\s*[\+\-\*\/]\s*\d+\s*[\+\-\*\/]\s*\d+\s*=\s*\??\s*$`, // 1+2*3= 或 1+2*3=?
	}

	for _, pattern := range patterns {
		if matched, _ := regexp.MatchString(pattern, query); matched {
			return true
		}
	}

	return false
}

// isSimpleQuery 检查是否是简单查询（单个技术词汇或过短查询）
func (ic *IntentClassifier) isSimpleQuery(query string) bool {
	query = strings.TrimSpace(query)
	queryLower := strings.ToLower(query)

	// 过短的查询（少于3个字符）
	if len(strings.ReplaceAll(query, " ", "")) < 3 {
		return true
	}

	// 单个技术词汇列表
	singleTechWords := []string{
		"go", "golang", "mysql", "localai", "embedding", "vector", "rag",
		"java", "python", "javascript", "react", "vue", "node", "docker",
		"redis", "mongodb", "postgresql", "nginx", "apache", "linux",
		"windows", "macos", "git", "github", "gitlab", "kubernetes",
	}

	// 检查是否是单个技术词汇
	for _, word := range singleTechWords {
		if queryLower == word {
			return true
		}
	}

	// 检查是否是没有问题上下文的短技术词汇
	if len(query) <= 8 {
		hasQuestionWord := false
		questionWords := []string{
			"什么", "如何", "怎么", "为什么", "是什么", "怎样", "介绍", "说明",
			"what", "how", "why", "explain", "tell", "about",
		}

		for _, qword := range questionWords {
			if strings.Contains(queryLower, qword) {
				hasQuestionWord = true
				break
			}
		}

		if !hasQuestionWord {
			return true
		}
	}

	return false
}

// hasStrongTechnicalIndicators 检查是否有强技术问题指示词
func (ic *IntentClassifier) hasStrongTechnicalIndicators(query string) bool {
	// 强技术问题指示词 - 这些词汇明确表示用户在询问技术问题
	strongIndicators := []string{
		"什么是", "如何", "怎么", "为什么", "原理", "实现", "配置", "安装", "部署",
		"介绍", "解释", "说明", "教程", "文档", "使用方法", "操作步骤",
		"what", "how", "why", "explain", "tutorial", "documentation",
	}

	for _, indicator := range strongIndicators {
		if strings.Contains(query, indicator) {
			return true
		}
	}

	// 检查是否是常见的编程语言或技术词汇（新增）
	technicalTerms := []string{
		"c#", "python", "java", "javascript", "typescript", "go", "rust", "swift", "kotlin",
		"php", "ruby", "c++", "c", "scala", "dart", "r", "matlab", "perl", "shell",
		"mysql", "postgresql", "mongodb", "redis", "elasticsearch", "sqlite",
		"react", "vue", "angular", "node", "express", "spring", "django", "flask",
		"docker", "kubernetes", "git", "linux", "windows", "macos", "aws", "azure",
		"localai", "openai", "chatgpt", "ai", "ml", "深度学习", "机器学习", "人工智能",
		"api", "rest", "graphql", "json", "xml", "yaml", "html", "css", "sql",
		"算法", "数据结构", "设计模式", "架构", "微服务", "分布式", "云计算",
	}

	queryLower := strings.ToLower(query)
	for _, term := range technicalTerms {
		if queryLower == term || strings.Contains(queryLower, term) {
			return true
		}
	}

	// 检查是否是完整的技术问题句式
	questionPatterns := []string{
		".*语言.*", ".*系统.*", ".*数据库.*", ".*框架.*", ".*工具.*",
		".*开发.*", ".*编程.*", ".*代码.*", ".*算法.*", ".*架构.*",
	}

	for _, pattern := range questionPatterns {
		if matched, _ := regexp.MatchString(pattern, query); matched {
			return true
		}
	}

	return false
}

// GetContext 获取对话上下文
func (cm *ContextManager) GetContext(userID string) *Conversation {
	if conv, exists := cm.conversations[userID]; exists {
		return conv
	}

	// 创建新的对话上下文
	conv := &Conversation{
		UserID:    userID,
		SessionID: fmt.Sprintf("session_%s_%d", userID, time.Now().Unix()),
		Messages:  make([]Message, 0),
		History:   make([]string, 0),
		LastTime:  time.Now(),
	}
	cm.conversations[userID] = conv
	return conv
}

// UpdateContext 更新对话上下文
func (cm *ContextManager) UpdateContext(userID, query, answer, intent string) {
	conv := cm.GetContext(userID)

	// 添加用户消息
	conv.Messages = append(conv.Messages, Message{
		Role:      "user",
		Content:   query,
		Timestamp: time.Now(),
		Intent:    intent,
	})

	// 添加助手回复
	conv.Messages = append(conv.Messages, Message{
		Role:      "assistant",
		Content:   answer,
		Timestamp: time.Now(),
	})

	// 更新查询历史
	conv.History = append(conv.History, query)
	if len(conv.History) > cm.maxHistory {
		conv.History = conv.History[1:] // 移除最早的查询
	}

	// 保持历史记录在限制范围内
	if len(conv.Messages) > cm.maxHistory*2 {
		conv.Messages = conv.Messages[2:] // 移除最早的一轮对话
	}

	conv.LastTime = time.Now()
}

// handleGreeting 处理问候
func (cs *ChatSystem) handleGreeting(query string, context *Conversation) *Response {
	// 🧠 智能问候处理

	// 检查是否是回访用户
	isReturning := len(context.Messages) > 0

	// 基于情感分析调整问候语调
	var greeting string

	if context.LastSentiment != "" {
		switch context.LastSentiment {
		case "positive":
			greeting = "😊 您好！感受到您的好心情，很高兴为您服务！"
		case "negative":
			greeting = "🤗 您好，我注意到您可能遇到了一些困难，让我来帮助您！"
		case "neutral":
			greeting = "👋 您好！我是您的专业技术助手，随时为您解答问题！"
		}
	}

	// 如果没有情感分析结果，使用时间问候
	if greeting == "" {
		if isReturning {
			greeting = "😊 欢迎回来！很高兴再次为您服务！"
		} else {
			hour := time.Now().Hour()
			if hour < 12 {
				greeting = "🌅 早上好！欢迎使用智能FAQ系统！"
			} else if hour < 18 {
				greeting = "☀️ 下午好！欢迎使用智能FAQ系统！"
			} else {
				greeting = "🌙 晚上好！欢迎使用智能FAQ系统！"
			}
		}
	}

	// 🧠 智能功能介绍
	capabilities := "\n\n🧠 **我的智能能力**：\n" +
		"• 🎯 **智能意图识别** - 准确理解您的需求\n" +
		"• 😊 **情感感知** - 根据您的情绪调整回应\n" +
		"• 🏷️ **实体识别** - 精确提取关键信息\n" +
		"• 💬 **多轮对话** - 记住上下文，连贯交流\n" +
		"• 📚 **知识学习** - 从交互中不断学习改进\n" +
		"• 🔍 **智能搜索** - 向量化语义匹配\n\n"

	// 基于历史交互提供个性化建议
	var personalizedSuggestion string
	if len(context.History) > 0 {
		personalizedSuggestion = "💡 **基于您的历史交互，我可以继续为您解答相关问题！**\n\n"
	} else {
		personalizedSuggestion = "💡 **您可以问我任何技术问题，比如**：\n" +
			"• \"什么是向量搜索？\"\n" +
			"• \"如何优化MySQL性能？\"\n" +
			"• \"Go语言有什么特点？\"\n\n"
	}

	answer := greeting + capabilities + personalizedSuggestion +
		"🚀 **现在就开始我们的智能对话吧！**"

	return &Response{
		Answer:     answer,
		Source:     "智能助手",
		Intent:     "greeting",
		Confidence: 1.0,
	}
}

// handleChatRequest 处理聊天请求
func (cs *ChatSystem) handleChatRequest(query string, context *Conversation) *Response {
	// 🧠 智能聊天响应

	// 基于上下文和情感状态生成个性化回应
	var response string

	// 检查用户的情感状态
	if context.LastSentiment == "negative" {
		response = "🤗 当然可以！我注意到您可能心情不太好，让我们聊聊天放松一下吧！\n\n" +
			"我不仅是一个技术助手，也是您的聊天伙伴。您可以：\n" +
			"• 💬 和我随意聊天，分享您的想法\n" +
			"• 🤔 问我任何感兴趣的问题\n" +
			"• 📚 让我为您解释复杂的技术概念\n" +
			"• 🎯 寻求解决问题的建议\n\n" +
			"😊 我会根据您的情绪和需求调整我的回应方式，让我们的对话更加自然愉快！"
	} else if context.LastSentiment == "positive" {
		response = "😄 太好了！我很乐意和您聊天！感受到您的好心情，这让我也很开心！\n\n" +
			"🎉 **我们可以聊很多有趣的话题**：\n" +
			"• 🚀 最新的技术趋势和创新\n" +
			"• 💡 编程技巧和最佳实践\n" +
			"• 🌟 您正在进行的项目\n" +
			"• 🤖 人工智能和未来科技\n" +
			"• 📈 职业发展和学习建议\n\n" +
			"💬 **我的聊天特色**：\n" +
			"• 🧠 智能理解上下文，记住我们的对话\n" +
			"• 😊 根据您的情绪调整回应风格\n" +
			"• 🎯 提供个性化的建议和解答\n\n" +
			"现在就开始我们的愉快对话吧！您想聊什么？"
	} else {
		response = "💬 当然可以！我很高兴能和您聊天！\n\n" +
			"🤖 **我是一个智能对话伙伴**，具备以下能力：\n" +
			"• 🧠 **智能理解** - 准确理解您的意图和情感\n" +
			"• 💭 **上下文记忆** - 记住我们对话的内容和背景\n" +
			"• 🎯 **个性化回应** - 根据您的风格调整我的表达\n" +
			"• 📚 **知识丰富** - 涵盖技术、生活、学习等多个领域\n\n" +
			"🌟 **我们可以聊**：\n" +
			"• 技术问题和编程挑战\n" +
			"• 学习方法和职业规划\n" +
			"• 日常生活和兴趣爱好\n" +
			"• 任何您感兴趣的话题\n\n" +
			"😊 我会努力让我们的对话既有趣又有价值！您想从什么话题开始呢？"
	}

	// 如果有历史对话，添加个性化元素
	if len(context.Messages) > 0 {
		response += "\n\n💡 **基于我们之前的对话**，我已经了解了您的一些偏好，会为您提供更贴心的回应！"
	}

	return &Response{
		Answer:     response,
		Source:     "智能助手",
		Intent:     "chat_request",
		Confidence: 1.0,
	}
}

// handleIdentityInquiry 处理身份询问
func (cs *ChatSystem) handleIdentityInquiry(query string, context *Conversation) *Response {
	// 根据上下文调整回答
	var intro string
	if len(context.Messages) > 0 {
		intro = "🤖 我是您一直在交流的智能FAQ技术助手！"
	} else {
		intro = "🤖 我是智能FAQ技术助手，很高兴认识您！"
	}

	answer := intro + "\n\n💡 我的能力包括：\n" +
		"• 理解您的问题意图\n" +
		"• 记住对话上下文\n" +
		"• 提供精准的技术解答\n" +
		"• 支持多轮深入交流\n" +
		"• 学习您的提问习惯\n\n" +
		"🎯 我专注于技术领域，让我们开始技术探讨吧！"

	return &Response{
		Answer:     answer,
		Source:     "智能助手",
		Intent:     "identity_inquiry",
		Confidence: 1.0,
	}
}

// handleIntelligentQuestion 智能问题处理 - 根据问题内容智能路由
func (cs *ChatSystem) handleIntelligentQuestion(query string, context *Conversation, nlpResult *nlp.IntegratedResult) *Response {
	logger.Info("🧠 智能问题分析: %s", query)

	// 🧠 基于问题内容进行智能路由
	queryLower := strings.ToLower(query)

	// 检查是否是身份询问
	identityKeywords := []string{"你是谁", "你是什么", "介绍一下", "你的身份", "你叫什么", "你的名字"}
	for _, keyword := range identityKeywords {
		if strings.Contains(queryLower, keyword) {
			logger.Info("🧠 智能路由: 身份询问 -> handleIdentityInquiry")
			return cs.handleIdentityInquiry(query, context)
		}
	}

	// 检查是否是问候
	greetingKeywords := []string{"你好", "hello", "hi", "早上好", "下午好", "晚上好"}
	for _, keyword := range greetingKeywords {
		if strings.Contains(queryLower, keyword) {
			logger.Info("🧠 智能路由: 问候 -> handleGreeting")
			return cs.handleGreeting(query, context)
		}
	}

	// 检查是否是聊天请求
	chatKeywords := []string{"聊天", "聊", "对话", "交流", "谈话", "陪我聊", "和我聊", "能聊", "可以聊"}
	for _, keyword := range chatKeywords {
		if strings.Contains(queryLower, keyword) {
			logger.Info("🧠 智能路由: 聊天请求 -> handleChatRequest")
			return cs.handleChatRequest(query, context)
		}
	}

	// 🔍 检查是否是技术问题（特别是AI相关）
	techKeywords := []string{
		"localai", "local ai", "人工智能", "机器学习", "深度学习", "神经网络",
		"ai", "ml", "nlp", "大模型", "llm", "chatgpt", "gpt", "向量", "embedding", "嵌入",
		"go语言", "golang", "python", "java", "javascript", "mysql", "数据库",
		"react", "vue", "docker", "kubernetes", "算法", "编程", "开发",
	}
	for _, keyword := range techKeywords {
		if strings.Contains(queryLower, keyword) {
			logger.Info("🧠 智能路由: 技术问题 -> handleTechnicalQuestion")
			response, err := cs.handleTechnicalQuestion(query, context)
			if err != nil {
				logger.Error("技术问题处理失败: %v", err)
				break // 如果技术问题处理失败，继续到通用处理
			}
			return response
		}
	}

	// 默认使用智能通用处理
	logger.Info("🧠 智能路由: 通用问题 -> handleIntelligentGeneral")
	response, err := cs.handleIntelligentGeneral(query, context, nlpResult)
	if err != nil {
		logger.Error("智能通用处理失败: %v", err)
		return &Response{
			Answer:     "🤔 抱歉，我在处理您的问题时遇到了一些困难，请稍后再试。",
			Source:     "错误处理",
			Intent:     "error",
			Confidence: 0.0,
		}
	}
	return response
}

// handleThanks 处理感谢
func (cs *ChatSystem) handleThanks(query string, context *Conversation) *Response {
	responses := []string{
		"😊 不客气！很高兴能帮助到您！",
		"🤗 不用谢！这是我应该做的！",
		"😄 能帮到您我很开心！",
	}

	// 根据对话历史选择回应
	responseIndex := len(context.Messages) % len(responses)
	answer := responses[responseIndex] + "\n\n如果您还有其他技术问题，请随时提问。我会继续为您提供专业的技术支持！"

	return &Response{
		Answer:     answer,
		Source:     "智能助手",
		Intent:     "thanks",
		Confidence: 1.0,
	}
}

// handleTechnicalQuestion 处理技术问题
func (cs *ChatSystem) handleTechnicalQuestion(query string, context *Conversation) (*Response, error) {
	// 0. 首先检查是否是特殊的技术问题，需要直接回答
	if specialAnswer := cs.handleSpecialTechnicalQuestions(query); specialAnswer != "" {
		return &Response{
			Answer:     specialAnswer,
			Source:     "技术知识库",
			Intent:     "technical_question",
			Confidence: 1.0,
		}, nil
	}

	// 1. 然后搜索学习到的知识
	if cs.knowledgeLearner != nil {
		learnedKnowledge, err := cs.knowledgeLearner.SearchLearnedKnowledge(query, 3)
		if err != nil {
			logger.Warnf("搜索学习知识失败: %v", err)
		} else if len(learnedKnowledge) > 0 {
			bestKnowledge := learnedKnowledge[0]
			logger.Infof("🔍 找到学习知识: %s (置信度: %.2f)", bestKnowledge.Question, bestKnowledge.Confidence)

			// 大幅降低置信度阈值，提高匹配率
			if bestKnowledge.Confidence > 0.1 {
				logger.Infof("🧠 使用学习知识回答: %s", bestKnowledge.Question)

				// 记录知识使用
				cs.knowledgeLearner.RecordKnowledgeUsage(bestKnowledge.ID, 0, context.UserID, bestKnowledge.Confidence, nil)

				// 安全检查：确保答案不是问题本身
				logger.Infof("🔍 安全检查: 问题='%s', 答案='%s'", strings.TrimSpace(query), strings.TrimSpace(bestKnowledge.Answer))
				if strings.TrimSpace(bestKnowledge.Answer) == strings.TrimSpace(query) {
					logger.Warnf("🚨 学习知识答案与问题相同，跳过: %s", query)
				} else {
					logger.Infof("✅ 安全检查通过，继续处理推荐")
					answer := bestKnowledge.Answer
					// 添加相关知识推荐
					logger.Infof("🔗 检查相关知识推荐: 知识点标志='%s', ID=%d", bestKnowledge.KnowledgeTopic, bestKnowledge.ID)

					// 无论如何都添加知识点标签（用于调试）
					if bestKnowledge.KnowledgeTopic != "" {
						answer += fmt.Sprintf("\n\n🏷️ **知识点标签**: %s", bestKnowledge.KnowledgeTopic)

						// 尝试获取相关知识
						relatedKnowledge := cs.getRelatedKnowledgeForTopic(bestKnowledge.KnowledgeTopic, bestKnowledge.ID)
						if relatedKnowledge != "" {
							logger.Infof("✅ 找到相关知识推荐，长度: %d", len(relatedKnowledge))
							answer += "\n\n" + relatedKnowledge
						} else {
							logger.Infof("❌ 未找到相关知识推荐")
						}
					} else {
						logger.Infof("❌ 知识点标志为空，跳过推荐")
					}

					return &Response{
						Answer:     answer,
						Source:     fmt.Sprintf("学习知识库 (置信度: %.1f%%)", bestKnowledge.Confidence*100),
						Intent:     "technical_question",
						Confidence: bestKnowledge.Confidence,
					}, nil
				}
			} else {
				logger.Infof("🤔 学习知识置信度不足: %.2f < 0.1", bestKnowledge.Confidence)
			}
		} else {
			logger.Infof("🔍 未找到匹配的学习知识")
		}
	}

	// 2. 尝试精确技术关键词匹配
	if exactAnswer := cs.tryExactTechnicalMatch(query); exactAnswer != "" {
		return &Response{
			Answer:     exactAnswer,
			Source:     "智能助手",
			Intent:     "technical_question",
			Confidence: 1.0,
		}, nil
	}

	// 3. 使用向量搜索
	return cs.handleVectorSearch(query, context)
}

// handleAlgorithmRequest 处理算法请求
func (cs *ChatSystem) handleAlgorithmRequest(query string, context *Conversation) (*Response, error) {
	// 使用算法识别器识别算法类型
	algorithmRequest := cs.algorithmRecognizer.RecognizeAlgorithm(query)

	// 降低置信度阈值，提高算法识别的敏感性
	if algorithmRequest.Type == algorithm.Unknown || algorithmRequest.Confidence < 0.1 {
		// 如果算法识别失败，尝试检查是否为简单数学表达式
		if cs.isSimpleMathQuery(query) {
			// 创建一个数学表达式请求
			algorithmRequest = &algorithm.AlgorithmRequest{
				Query:      query,
				Type:       algorithm.MathExpression,
				Expression: cs.extractMathFromQuery(query),
				Parameters: make(map[string]interface{}),
				Confidence: 0.8,
			}
		} else {
			// 回退到技术问题处理
			return cs.handleTechnicalQuestion(query, context)
		}
	}

	// 执行算法
	result := cs.algorithmExecutor.Execute(algorithmRequest)

	if !result.Success {
		// 执行失败，提供错误信息
		answer := fmt.Sprintf("🤔 算法执行遇到问题：%s\n\n", result.Error)
		answer += "💡 建议：\n"
		answer += "• 检查输入格式是否正确\n"
		answer += "• 确认算法参数是否完整\n"
		answer += "• 尝试重新描述您的问题\n\n"
		answer += "我可以帮您解答各种算法问题，包括：\n"
		answer += "• 数学计算：如 '计算 2+3*4'\n"
		answer += "• 排序算法：如 '冒泡排序 [3,1,4,1,5]'\n"
		answer += "• 搜索算法：如 '二分查找 23 在 [1,5,12,23,45]'\n"
		answer += "• 统计分析：如 '计算平均数 [85,92,78,96,88]'"

		return &Response{
			Answer:     answer,
			Source:     "算法引擎",
			Intent:     "algorithm_request",
			Confidence: 0.7,
		}, nil
	}

	// 成功执行，返回结果
	answer := result.Explanation

	// 添加算法类型信息
	algorithmTypeName := cs.algorithmRecognizer.GetAlgorithmTypeName(algorithmRequest.Type)
	answer += fmt.Sprintf("\n\n🔍 **算法类型**: %s\n", algorithmTypeName)
	answer += fmt.Sprintf("🎯 **识别置信度**: %.1f%%", algorithmRequest.Confidence*100)

	return &Response{
		Answer:     answer,
		Source:     "算法引擎",
		Intent:     "algorithm_request",
		Confidence: algorithmRequest.Confidence,
	}, nil
}

// handleFollowUp 处理追问
func (cs *ChatSystem) handleFollowUp(query string, context *Conversation) (*Response, error) {
	// 分析上一轮对话
	if len(context.Messages) < 2 {
		return cs.handleGeneral(query, context)
	}

	lastUserMsg := ""
	for i := len(context.Messages) - 1; i >= 0; i-- {
		if context.Messages[i].Role == "user" {
			lastUserMsg = context.Messages[i].Content
			break
		}
	}

	// 构建上下文化的追问回答
	contextualQuery := lastUserMsg + " " + query
	return cs.handleTechnicalQuestion(contextualQuery, context)
}

// handleIntelligentGeneral 智能通用处理
func (cs *ChatSystem) handleIntelligentGeneral(query string, context *Conversation, nlpResult *nlp.IntegratedResult) (*Response, error) {
	// 🧠 基于NLP分析结果进行智能处理

	// 0. 首先搜索学习到的知识（最高优先级）
	if cs.knowledgeLearner != nil {
		learnedKnowledge, err := cs.knowledgeLearner.SearchLearnedKnowledge(query, 3)
		if err != nil {
			logger.Warnf("搜索学习知识失败: %v", err)
		} else if len(learnedKnowledge) > 0 {
			bestKnowledge := learnedKnowledge[0]
			logger.Infof("🔍 找到学习知识: %s (置信度: %.2f)", bestKnowledge.Question, bestKnowledge.Confidence)

			// 大幅降低置信度阈值，提高匹配率
			if bestKnowledge.Confidence > 0.1 {
				logger.Infof("🧠 使用学习知识回答: %s", bestKnowledge.Question)

				// 安全检查：确保答案不是问题本身
				if strings.TrimSpace(bestKnowledge.Answer) == strings.TrimSpace(query) {
					logger.Warnf("🚨 学习知识答案与问题相同，跳过: %s", query)
				} else {
					// 记录知识使用
					cs.knowledgeLearner.RecordKnowledgeUsage(bestKnowledge.ID, 0, context.UserID, bestKnowledge.Confidence, nil)

					answer := bestKnowledge.Answer
					// 添加相关知识推荐
					logger.Infof("🔗 检查相关知识推荐: 知识点标志='%s', ID=%d", bestKnowledge.KnowledgeTopic, bestKnowledge.ID)

					// 无论如何都添加知识点标签（用于调试）
					if bestKnowledge.KnowledgeTopic != "" {
						answer += fmt.Sprintf("\n\n🏷️ **知识点标签**: %s", bestKnowledge.KnowledgeTopic)

						// 尝试获取相关知识
						relatedKnowledge := cs.getRelatedKnowledgeForTopic(bestKnowledge.KnowledgeTopic, bestKnowledge.ID)
						if relatedKnowledge != "" {
							logger.Infof("✅ 找到相关知识推荐，长度: %d", len(relatedKnowledge))
							answer += "\n\n" + relatedKnowledge
						} else {
							logger.Infof("❌ 未找到相关知识推荐")
						}
					} else {
						logger.Infof("❌ 知识点标志为空，跳过推荐")
					}

					return &Response{
						Answer:     answer,
						Source:     fmt.Sprintf("学习知识库 (置信度: %.1f%%)", bestKnowledge.Confidence*100),
						Intent:     "learned_knowledge",
						Confidence: bestKnowledge.Confidence,
					}, nil
				}
			} else {
				logger.Infof("🤔 学习知识置信度不足: %.2f < 0.1", bestKnowledge.Confidence)
			}
		} else {
			logger.Infof("🔍 未找到匹配的学习知识")
		}
	}

	// 1. 检查是否是聊天请求（通过NLP分析）
	if nlpResult != nil && nlpResult.Intent != nil {
		// 检查是否包含聊天相关的意图
		queryLower := strings.ToLower(query)
		chatKeywords := []string{"聊天", "聊", "对话", "交流", "谈话", "chat", "talk", "conversation"}

		for _, keyword := range chatKeywords {
			if strings.Contains(queryLower, keyword) {
				// 这是一个聊天请求，重新路由到聊天处理
				return cs.handleChatRequest(query, context), nil
			}
		}

		// 2. 基于实体识别进行智能路由
		if len(nlpResult.ExtractedEntities) > 0 {
			for _, entity := range nlpResult.ExtractedEntities {
				if entity.Type == "TECH" || entity.Type == "PROGRAMMING_LANGUAGE" {
					// 这是技术问题，路由到技术处理
					return cs.handleTechnicalQuestion(query, context)
				}
			}
		}

		// 3. 基于情感分析调整处理策略
		if nlpResult.SentimentDetail != nil {
			if nlpResult.SentimentDetail.Label == "negative" && nlpResult.SentimentDetail.Score < -0.5 {
				// 用户情绪不好，提供更温暖的回应
				response, err := cs.handleGeneral(query, context)
				if err == nil && response != nil {
					// 在回应前添加关怀语句
					response.Answer = "🤗 我感觉到您可能遇到了一些困难，让我来帮助您：\n\n" + response.Answer
				}
				return response, err
			}
		}
	}

	// 4. 回退到标准通用处理
	return cs.handleGeneral(query, context)
}

// handleGeneral 处理一般问题
func (cs *ChatSystem) handleGeneral(query string, context *Conversation) (*Response, error) {
	// 1. 首先搜索学习到的知识（最重要的改进！）
	if cs.knowledgeLearner != nil {
		learnedKnowledge, err := cs.knowledgeLearner.SearchLearnedKnowledge(query, 3)
		if err != nil {
			logger.Warnf("搜索学习知识失败: %v", err)
		} else if len(learnedKnowledge) > 0 {
			bestKnowledge := learnedKnowledge[0]
			logger.Infof("🔍 找到学习知识: %s (置信度: %.2f)", bestKnowledge.Question, bestKnowledge.Confidence)

			// 大幅降低置信度阈值，提高匹配率
			if bestKnowledge.Confidence > 0.1 {
				logger.Infof("🧠 使用学习知识回答: %s", bestKnowledge.Question)

				// 安全检查：确保答案不是问题本身
				if strings.TrimSpace(bestKnowledge.Answer) == strings.TrimSpace(query) {
					logger.Warnf("🚨 学习知识答案与问题相同，跳过: %s", query)
				} else {
					// 记录知识使用
					cs.knowledgeLearner.RecordKnowledgeUsage(bestKnowledge.ID, 0, context.UserID, bestKnowledge.Confidence, nil)

					answer := bestKnowledge.Answer
					// 添加相关知识推荐
					logger.Infof("🔗 检查相关知识推荐: 知识点标志='%s', ID=%d", bestKnowledge.KnowledgeTopic, bestKnowledge.ID)

					// 无论如何都添加知识点标签（用于调试）
					if bestKnowledge.KnowledgeTopic != "" {
						answer += fmt.Sprintf("\n\n🏷️ **知识点标签**: %s", bestKnowledge.KnowledgeTopic)

						// 尝试获取相关知识
						relatedKnowledge := cs.getRelatedKnowledgeForTopic(bestKnowledge.KnowledgeTopic, bestKnowledge.ID)
						if relatedKnowledge != "" {
							logger.Infof("✅ 找到相关知识推荐，长度: %d", len(relatedKnowledge))
							answer += "\n\n" + relatedKnowledge
						} else {
							logger.Infof("❌ 未找到相关知识推荐")
						}
					} else {
						logger.Infof("❌ 知识点标志为空，跳过推荐")
					}

					return &Response{
						Answer:     answer,
						Source:     fmt.Sprintf("学习知识库 (置信度: %.1f%%)", bestKnowledge.Confidence*100),
						Intent:     "learned_knowledge",
						Confidence: bestKnowledge.Confidence,
					}, nil
				}
			} else {
				logger.Infof("🤔 学习知识置信度不足: %.2f < 0.1", bestKnowledge.Confidence)
			}
		} else {
			logger.Infof("🔍 未找到匹配的学习知识")
		}
	}

	// 2. 检查是否是单个技术词汇，如果是，先尝试搜索FAQ
	if cs.isSingleTechnicalWord(query) {
		// 尝试向量搜索FAQ中的相关内容
		response, err := cs.handleVectorSearch(query, context)
		if err == nil && response.Confidence > 0.5 {
			// 如果找到了相关的FAQ答案，直接返回
			// 为单个技术词汇的回答添加特殊标识
			response.Answer = "📚 **相关技术资料**\n\n" + response.Answer
			return response, nil
		}

		// 如果没有找到相关答案，提供引导性回答
		return cs.handleLowQualityTechnicalQuery(query), nil
	}

	// 3. 对于其他一般问题，尝试向量搜索
	return cs.handleVectorSearch(query, context)
}

// tryExactTechnicalMatch 尝试精确技术匹配 - 只返回真正的精确匹配
func (cs *ChatSystem) tryExactTechnicalMatch(question string) string {
	// 这个方法应该只返回真正的精确匹配，不返回兜底回答
	// 对于没有精确匹配的情况，返回空字符串让系统走向量搜索流程

	// 检查是否是非常简单的问候或无意义输入
	q := strings.ToLower(strings.TrimSpace(question))

	// 只对极短或无意义的输入返回引导回答
	if len(q) <= 2 {
		switch q {
		case "你", "我", "他", "她", "它":
			return "🤔 请细说，希望能帮到您！我是技术FAQ助手，可以为您解答：\n\n• LocalAI和AI技术\n• MySQL数据库\n• 向量搜索\n• Go语言开发\n• 系统部署\n\n请详细描述您的问题～"
		case "好", "行", "嗯", "哦", "啊":
			return "😊 请细说您想了解的技术问题，我会尽力为您解答！"
		case "hi", "ok":
			return "👋 Hello! 请告诉我您想了解什么技术问题吧！"
		}
	}

	// 对于其他所有情况，返回空字符串，让系统走向量搜索流程
	return ""
}

// handleVectorSearch 处理向量搜索
func (cs *ChatSystem) handleVectorSearch(query string, context *Conversation) (*Response, error) {
	// 检查必要的组件是否可用
	if cs.embedClient == nil || cs.vectorStore == nil {
		// 如果向量搜索组件不可用，直接返回诚实的无匹配回答
		return cs.generateNoMatchResponse(query), nil
	}

	// 生成查询向量
	queryEmbedding, err := cs.embedClient.EmbedText(query)
	if err != nil {
		// 向量生成失败，返回诚实的无匹配回答
		return cs.generateNoMatchResponse(query), nil
	}

	// 🧠 第一步：NLP处理器分析查询
	nlpResult, err := cs.performNLPAnalysis(query)
	if err != nil {
		logger.Errorf("NLP分析失败: %v", err)
		// 继续执行，但记录错误
	}

	// 向量搜索
	results, err := cs.vectorStore.SearchTopK(queryEmbedding, 5) // 增加候选数量
	if err != nil || len(results) == 0 {
		// 向量搜索失败或无结果，返回诚实的无匹配回答
		return cs.generateNoMatchResponse(query), nil
	}

	// 🧠 第二步：使用NLP结果过滤向量搜索结果
	nlpFilteredResults := cs.applyNLPFilterToVectorResults(query, results, nlpResult)
	if len(nlpFilteredResults) == 0 {
		logger.Infof("🚫 所有向量搜索结果都被NLP过滤器拒绝")
		return cs.generateNoMatchResponse(query), nil
	}

	// 🛡️ 第三步：应用主题检查过滤
	topicFilteredResults := cs.applyTopicFilterToVectorResults(query, nlpFilteredResults)
	if len(topicFilteredResults) == 0 {
		logger.Infof("🚫 所有向量搜索结果都被主题检查拒绝")
		return cs.generateNoMatchResponse(query), nil
	}

	// 🎯 第四步：使用智能匹配器找到最佳匹配
	bestMatch := cs.semanticMatcher.FindBestMatch(query, topicFilteredResults)
	if bestMatch == nil {
		return cs.generateFallbackResponse(query), nil
	}

	// 应用智能进化匹配（如果可用）
	if cs.evolutionMatcher != nil {
		// 创建匹配上下文
		userSegment, thinkingStyle := cs.evolutionMatcher.GetUserThinkingStyle(context.UserID)
		matchingContext := &learning.MatchingContext{
			Query:         query,
			UserID:        context.UserID,
			Intent:        "technical_question",
			UserSegment:   userSegment,
			ThinkingStyle: thinkingStyle,
			SessionLength: len(context.History),
			PreviousQuery: cs.getPreviousQuery(context),
			Metadata:      make(map[string]interface{}),
		}

		// 应用智能匹配优化
		matchingResult := cs.evolutionMatcher.ApplySmartMatching(matchingContext, bestMatch.Score)

		// 更新匹配分数和响应风格
		bestMatch.Score = matchingResult.AdjustedScore

		// 根据学习到的偏好调整响应
		if matchingResult.ResponseStyle == "concise" {
			// 用户偏好简洁回答
			bestMatch.Explanation = "" // 移除详细解释
		}

		logger.Infof("🧠 智能进化匹配: %.3f -> %.3f (规则: %v)",
			matchingResult.OriginalScore, matchingResult.AdjustedScore, matchingResult.AppliedRules)
	}

	// 根据匹配质量生成不同的回答
	var answer string
	var source string

	if bestMatch.Score > 0.8 {
		// 高质量匹配，直接返回答案
		answer = bestMatch.FAQ.Answer
		source = "FAQ数据库（精确匹配）"
	} else if bestMatch.Score > 0.6 {
		// 中等质量匹配，添加说明
		answer = fmt.Sprintf("🎯 **%s**\n\n%s\n\n💡 %s",
			bestMatch.MatchType, bestMatch.FAQ.Answer, bestMatch.Explanation)
		source = "FAQ数据库（语义匹配）"
	} else if bestMatch.Score > 0.4 {
		// 低质量匹配，提供相关信息但说明可能不完全匹配
		answer = fmt.Sprintf("🔍 我找到了一些可能相关的信息（相关度：%.1f%%）：\n\n%s\n\n⚠️ **注意**：这个答案可能不完全符合您的问题。如果您需要更具体的信息，建议您：\n• 提供更详细的问题描述\n• 尝试换个方式提问\n• 或者告诉我您想了解的具体方面",
			bestMatch.Score*100, bestMatch.FAQ.Answer)
		source = "FAQ数据库（低匹配度）"
	} else {
		// 匹配度太低，诚实告知没有找到合适答案
		return cs.generateNoMatchResponse(query), nil
	}

	// 🛡️ 安全检查1：确保答案不是问题本身
	if strings.TrimSpace(answer) == strings.TrimSpace(query) {
		logger.Warnf("🚨 检测到答案与问题相同，使用兜底回答: %s", query)
		return cs.generateNoMatchResponse(query), nil
	}

	// 🛡️ 安全检查2：确保答案不是问题格式
	if cs.isAnswerActuallyQuestion(answer) {
		logger.Warnf("🚨 检测到答案是问题格式，使用兜底回答: %s", answer)
		return cs.generateNoMatchResponse(query), nil
	}

	// 🔗 添加相关知识点推荐
	relatedKnowledge := cs.getRelatedKnowledgeRecommendations(query, bestMatch.FAQ.Answer)
	if relatedKnowledge != "" {
		answer += "\n\n" + relatedKnowledge
	}

	return &Response{
		Answer:     answer,
		Source:     source,
		Intent:     "technical_question",
		Confidence: bestMatch.Score,
	}, nil
}

// getFAQByID 根据ID获取FAQ
func (cs *ChatSystem) getFAQByID(id int) *mysql.FAQ {
	for _, faq := range cs.faqs {
		if faq.ID == id {
			return &faq
		}
	}
	return nil
}

// formatConfidence 格式化置信度
func formatConfidence(confidence float32) string {
	return strings.TrimSuffix(strings.TrimSuffix(fmt.Sprintf("%.1f%%", confidence*100), "0"), ".") + "%"
}

// generateFallbackResponse 生成兜底回答 - 使用智能匹配器
func (cs *ChatSystem) generateFallbackResponse(query string) *Response {
	answer := cs.smartMatcher.GenerateFallbackAnswer(query)

	return &Response{
		Answer:     answer,
		Source:     "智能助手",
		Intent:     "general",
		Confidence: 0.5,
	}
}

// isSimpleMathQuery 检查是否为简单数学查询
func (cs *ChatSystem) isSimpleMathQuery(query string) bool {
	query = strings.ToLower(strings.TrimSpace(query))

	// 检查是否包含数字和运算符
	hasNumber := false
	hasOperator := false

	for _, char := range query {
		if char >= '0' && char <= '9' {
			hasNumber = true
		}
		if strings.ContainsRune("+-*/=×÷", char) {
			hasOperator = true
		}
	}

	// 检查是否包含计算相关词汇
	mathKeywords := []string{"计算", "求", "等于", "多少", "加", "减", "乘", "除"}
	hasMathKeyword := false
	for _, keyword := range mathKeywords {
		if strings.Contains(query, keyword) {
			hasMathKeyword = true
			break
		}
	}

	return (hasNumber && hasOperator) || (hasNumber && hasMathKeyword)
}

// extractMathFromQuery 从查询中提取数学表达式
func (cs *ChatSystem) extractMathFromQuery(query string) string {
	// 首先尝试使用正则表达式直接提取数学表达式
	if mathExpr := cs.extractPureMathExpression(query); mathExpr != "" {
		return mathExpr
	}

	// 移除常见的中文描述词
	removeWords := []string{
		"帮我", "请", "帮", "我", "计算", "算", "求", "一下", "多少", "结果", "答案", "是", "的", "什么", "等于",
	}

	cleaned := query
	for _, word := range removeWords {
		cleaned = strings.ReplaceAll(cleaned, word, "")
	}

	// 处理冒号分隔符
	if strings.Contains(cleaned, "：") {
		parts := strings.Split(cleaned, "：")
		if len(parts) > 1 {
			cleaned = parts[len(parts)-1] // 取冒号后面的部分
		}
	}
	if strings.Contains(cleaned, ":") {
		parts := strings.Split(cleaned, ":")
		if len(parts) > 1 {
			cleaned = parts[len(parts)-1] // 取冒号后面的部分
		}
	}

	// 处理等号情况：如果表达式以 = 或 =? 结尾，提取等号前面的部分
	if strings.HasSuffix(cleaned, "=?") {
		cleaned = strings.TrimSuffix(cleaned, "=?")
	} else if strings.HasSuffix(cleaned, "=") {
		cleaned = strings.TrimSuffix(cleaned, "=")
	}

	// 处理问号
	cleaned = strings.ReplaceAll(cleaned, "?", "")

	// 替换中文运算符
	cleaned = strings.ReplaceAll(cleaned, "加", "+")
	cleaned = strings.ReplaceAll(cleaned, "减", "-")
	cleaned = strings.ReplaceAll(cleaned, "乘", "*")
	cleaned = strings.ReplaceAll(cleaned, "除", "/")
	cleaned = strings.ReplaceAll(cleaned, "×", "*")
	cleaned = strings.ReplaceAll(cleaned, "÷", "/")

	// 移除多余空格
	cleaned = strings.TrimSpace(cleaned)

	return cleaned
}

// extractPureMathExpression 使用正则表达式直接提取纯数学表达式
func (cs *ChatSystem) extractPureMathExpression(text string) string {
	// 匹配数学表达式模式：数字、运算符、括号、小数点、空格
	patterns := []string{
		`\d+(?:\.\d+)?\s*[\+\-\*\/]\s*\d+(?:\.\d+)?(?:\s*[\+\-\*\/]\s*\d+(?:\.\d+)?)*`, // 基本四则运算
		`\d+(?:\.\d+)?\s*[\+\-\*\/]\s*\d+(?:\.\d+)?`,                                   // 简单两数运算
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		if match := re.FindString(text); match != "" {
			return strings.TrimSpace(match)
		}
	}

	return ""
}

// isLowQualityTechnicalQuery 检查是否是低质量的技术查询
func (cs *ChatSystem) isLowQualityTechnicalQuery(query string) bool {
	query = strings.TrimSpace(query)

	// 过短的查询（少于3个字符）
	if len(strings.ReplaceAll(query, " ", "")) < 3 {
		return true
	}

	// 单个技术词汇，没有问题上下文
	singleTechWords := []string{
		"go", "golang", "mysql", "localai", "embedding", "vector", "rag",
		"java", "python", "javascript", "react", "vue", "node", "docker",
	}

	queryLower := strings.ToLower(query)
	for _, word := range singleTechWords {
		if queryLower == word {
			return true
		}
	}

	// 没有问题词汇的技术词汇
	hasQuestionWord := false
	questionWords := []string{
		"什么", "如何", "怎么", "为什么", "是什么", "怎样", "介绍", "说明",
		"what", "how", "why", "explain", "tell", "about",
	}

	for _, qword := range questionWords {
		if strings.Contains(queryLower, qword) {
			hasQuestionWord = true
			break
		}
	}

	// 如果查询很短且没有问题词汇，认为是低质量查询
	if len(query) < 6 && !hasQuestionWord {
		return true
	}

	return false
}

// handleLowQualityTechnicalQuery 处理低质量技术查询
func (cs *ChatSystem) handleLowQualityTechnicalQuery(query string) *Response {
	query = strings.ToLower(strings.TrimSpace(query))

	// 根据查询内容提供引导性回答
	var answer string

	switch query {
	case "go", "golang":
		answer = "🔍 您想了解Go语言的什么方面呢？\n\n" +
			"我可以为您介绍：\n" +
			"• Go语言基础语法和特性\n" +
			"• Go语言开发环境配置\n" +
			"• Go语言项目结构和最佳实践\n" +
			"• Go语言并发编程\n" +
			"• Go语言Web开发\n\n" +
			"💡 请具体描述您的问题，比如：\n" +
			"- \"Go语言有什么特点？\"\n" +
			"- \"如何安装Go开发环境？\"\n" +
			"- \"Go语言适合做什么项目？\""

	case "mysql":
		answer = "🗄️ 您想了解MySQL的什么内容呢？\n\n" +
			"我可以为您介绍：\n" +
			"• MySQL数据库安装和配置\n" +
			"• SQL语法和查询优化\n" +
			"• 数据库设计和建模\n" +
			"• MySQL性能调优\n" +
			"• 数据备份和恢复\n\n" +
			"💡 请具体描述您的问题，比如：\n" +
			"- \"如何安装MySQL？\"\n" +
			"- \"MySQL索引如何优化？\"\n" +
			"- \"如何设计数据库表结构？\""

	case "localai":
		answer = "🤖 您想了解LocalAI的什么方面呢？\n\n" +
			"我可以为您介绍：\n" +
			"• LocalAI的安装和部署\n" +
			"• 模型配置和管理\n" +
			"• API接口使用方法\n" +
			"• 性能优化和故障排除\n\n" +
			"💡 请具体描述您的问题，比如：\n" +
			"- \"如何安装LocalAI？\"\n" +
			"- \"LocalAI支持哪些模型？\"\n" +
			"- \"如何配置LocalAI的API？\""

	default:
		answer = "🤔 您的问题似乎比较简短，我想为您提供更准确的帮助！\n\n" +
			"💡 建议您可以这样提问：\n" +
			"• 使用疑问词：\"什么是...\"、\"如何...\"、\"为什么...\"\n" +
			"• 描述具体场景：\"我想要实现...\"、\"遇到了...问题\"\n" +
			"• 提供上下文：\"在使用...时，出现了...\"\n\n" +
			"🎯 这样我就能为您提供更精准和有用的技术解答了！"
	}

	return &Response{
		Answer:     answer,
		Source:     "智能助手",
		Intent:     "technical_question",
		Confidence: 0.8,
	}
}

// isSingleTechnicalWord 检查是否是单个技术词汇
func (cs *ChatSystem) isSingleTechnicalWord(query string) bool {
	query = strings.ToLower(strings.TrimSpace(query))

	// 单个技术词汇列表
	singleTechWords := []string{
		"go", "golang", "mysql", "localai", "embedding", "vector", "rag",
		"java", "python", "javascript", "react", "vue", "node", "docker",
		"redis", "mongodb", "postgresql", "nginx", "apache", "linux",
		"windows", "macos", "git", "github", "gitlab", "kubernetes",
	}

	// 检查是否是单个技术词汇
	for _, word := range singleTechWords {
		if query == word {
			return true
		}
	}

	return false
}

// generateNoMatchResponse 生成没有匹配时的诚实回答
func (cs *ChatSystem) generateNoMatchResponse(query string) *Response {
	// 分析查询类型，提供针对性的建议
	var answer string

	// 检查是否是常见的非技术问题，提供更友好的回答
	if cs.isCommonNonTechnicalQuestion(query) {
		answer = cs.generateCommonQuestionResponse(query)
	} else if cs.isTechnicalQuery(query) {
		// 🎯 识别具体的技术主题并提供针对性建议
		answer = cs.generateTopicSpecificResponse(query)
	} else {
		answer = fmt.Sprintf("🤔 抱歉，我没有找到关于「%s」的相关信息。\n\n💡 **建议您可以：**\n• 换个方式描述您的问题\n• 提供更多具体信息\n• 或者问我其他相关问题\n\n我主要可以帮您解答技术相关的问题哦！", query)
	}

	return &Response{
		Answer:     answer,
		Source:     "智能助手",
		Intent:     "no_match",
		Confidence: 0.0,
	}
}

// isTechnicalQuery 检查是否是技术查询
func (cs *ChatSystem) isTechnicalQuery(query string) bool {
	techKeywords := []string{
		// 编程语言
		"go", "golang", "java", "python", "javascript", "node", "c++", "csharp", "rust", "php", "ruby",
		// 数据库
		"mysql", "redis", "postgresql", "mongodb", "sqlite", "oracle",
		// AI和机器学习
		"localai", "local ai", "人工智能", "机器学习", "深度学习", "神经网络",
		"ai", "ml", "nlp", "大模型", "llm", "chatgpt", "gpt", "向量", "embedding", "嵌入",
		// 框架和工具
		"docker", "kubernetes", "react", "vue", "angular", "django", "flask", "spring",
		// 系统和部署
		"linux", "windows", "配置", "安装", "部署", "开发", "编程", "代码", "系统", "数据库", "服务器", "网络",
	}

	queryLower := strings.ToLower(query)
	for _, keyword := range techKeywords {
		if strings.Contains(queryLower, keyword) {
			return true
		}
	}

	return false
}

// isCommonNonTechnicalQuestion 检查是否是常见的非技术问题
func (cs *ChatSystem) isCommonNonTechnicalQuestion(query string) bool {
	q := strings.ToLower(strings.TrimSpace(query))

	// 常见的非技术问题模式
	commonPatterns := []string{
		"手机", "电话", "汽车", "车", "房子", "房屋", "食物", "食品", "衣服", "服装",
		"动物", "植物", "天气", "时间", "地点", "人物", "历史", "文化", "艺术",
		"音乐", "电影", "书籍", "运动", "游戏", "旅游", "健康", "医疗", "法律",
		"金融", "股票", "投资", "教育", "学校", "老师", "学生", "家庭", "朋友",
	}

	for _, pattern := range commonPatterns {
		if strings.Contains(q, pattern) {
			return true
		}
	}

	return false
}

// generateCommonQuestionResponse 为常见非技术问题生成回答
func (cs *ChatSystem) generateCommonQuestionResponse(query string) string {
	q := strings.ToLower(strings.TrimSpace(query))

	// 针对不同类型的问题提供不同的回答
	if strings.Contains(q, "手机") {
		return "📱 手机是一种便携式通信设备，也称为移动电话或手机。\n\n🔧 **基本功能：**\n• 语音通话和短信\n• 移动互联网接入\n• 应用程序运行\n• 拍照和录像\n• 音乐和视频播放\n\n💡 **现代智能手机特点：**\n• 触摸屏操作界面\n• 强大的处理器和内存\n• 多种传感器（GPS、陀螺仪等）\n• 丰富的应用生态系统\n\n虽然我主要专注于技术开发问题，但希望这个回答对您有帮助！如果您有技术开发相关的问题，我会更专业地为您解答。"
	}

	if strings.Contains(q, "汽车") || strings.Contains(q, "车") {
		return "🚗 汽车是一种陆地交通工具，通常有四个轮子，由发动机驱动。\n\n⚙️ **主要组成：**\n• 发动机系统\n• 传动系统\n• 制动系统\n• 转向系统\n• 电气系统\n\n🔋 **现代发展：**\n• 电动汽车\n• 自动驾驶技术\n• 智能车载系统\n\n我主要专注于软件技术问题，如果您对汽车软件开发、车载系统等技术方面有疑问，我很乐意为您详细解答！"
	}

	// 通用回答
	return fmt.Sprintf("🤔 关于「%s」，这是一个很有趣的问题！\n\n💡 **我的专长领域：**\n我主要专注于技术开发相关的问题，包括：\n• 编程语言和开发框架\n• 数据库和系统架构\n• AI和机器学习技术\n• 软件部署和运维\n\n🌐 **获取更多信息：**\n• 可以尝试搜索引擎获取详细信息\n• 查看相关的专业网站或百科\n• 咨询该领域的专业人士\n\n如果您有技术开发方面的问题，我会很专业地为您解答！", query)
}

// handleSpecialTechnicalQuestions 处理特殊的技术问题
func (cs *ChatSystem) handleSpecialTechnicalQuestions(query string) string {
	q := strings.ToLower(strings.TrimSpace(query))

	// C#相关问题的特殊处理
	if strings.Contains(q, "c#") {
		// 各种C#问题的变体
		if strings.Contains(q, "什么是") || strings.Contains(q, "是什么") ||
			strings.Contains(q, "什么语言") || strings.Contains(q, "语言") ||
			q == "c#" {
			return "💻 C#是微软开发的一种现代面向对象编程语言。\n\n🌟 **主要特点：**\n• 强类型、面向对象的编程语言\n• 运行在.NET框架上\n• 语法简洁，易于学习和使用\n• 强大的内存管理和垃圾回收\n• 丰富的类库和框架支持\n\n🚀 **应用领域：**\n• Web应用开发（ASP.NET）\n• 桌面应用程序（WPF、WinForms）\n• 移动应用开发（Xamarin）\n• 游戏开发（Unity）\n• 云服务和微服务\n\n💡 **版本发展：**\n• 从C# 1.0到最新的C# 12\n• 持续增加新特性和语法糖\n• 跨平台支持（.NET Core/.NET 5+）\n\n这是我的专业领域！如果您有C#开发相关的具体问题，我很乐意为您详细解答。"
		}
	}

	// Python相关问题的特殊处理
	if strings.Contains(q, "python") {
		if strings.Contains(q, "什么是") || strings.Contains(q, "是什么") ||
			strings.Contains(q, "什么语言") || strings.Contains(q, "语言") ||
			q == "python" {
			return "🐍 Python是一种高级编程语言，以其简洁的语法和强大的功能而闻名。\n\n🌟 **主要特点：**\n• 语法简洁易读，学习曲线平缓\n• 解释型语言，开发效率高\n• 丰富的第三方库生态系统\n• 跨平台支持\n• 强大的社区支持\n\n🚀 **应用领域：**\n• Web开发（Django、Flask）\n• 数据科学和机器学习\n• 自动化脚本和工具\n• 科学计算\n• 人工智能开发\n\n💡 **特色优势：**\n• \"人生苦短，我用Python\"\n• 适合快速原型开发\n• 大量优秀的开源项目\n\n如果您想了解Python的具体应用或开发问题，我很乐意为您详细解答！"
		}
	}

	// 其他技术问题可以在这里添加

	return "" // 没有特殊处理，返回空字符串
}

// handleFeedback 处理用户反馈
func (cs *ChatSystem) handleFeedback(query string, context *Conversation) *Response {
	logger.Info("😊 处理用户反馈: %s", query)

	queryLower := strings.ToLower(strings.TrimSpace(query))

	// 根据不同的反馈类型给出不同的回应
	var responseText string
	var confidence float32 = 0.95

	// 正面反馈
	positiveWords := []string{"不错", "很好", "好的", "棒", "赞", "厉害", "优秀", "完美", "太好了", "好", "ok", "满意", "可以", "行", "没问题"}
	for _, word := range positiveWords {
		if queryLower == word {
			responses := []string{
				"😊 谢谢您的肯定！我会继续努力为您提供更好的服务。",
				"🎉 很高兴能帮到您！如果还有其他问题，随时可以问我。",
				"😄 您的认可是我最大的动力！有什么其他需要帮助的吗？",
				"🌟 感谢您的反馈！我会持续学习和改进。",
				"👍 太好了！如果您还有其他疑问，我随时为您解答。",
			}
			// 简单的随机选择（基于查询长度）
			index := len(query) % len(responses)
			responseText = responses[index]
			break
		}
	}

	// 感谢类反馈
	thanksWords := []string{"谢谢", "感谢", "多谢", "thanks"}
	for _, word := range thanksWords {
		if queryLower == word {
			responses := []string{
				"😊 不客气！很高兴能帮助到您。",
				"🤝 这是我应该做的！有其他问题随时问我。",
				"😄 能为您提供帮助是我的荣幸！",
				"🌟 不用谢！我们随时为您服务。",
			}
			index := len(query) % len(responses)
			responseText = responses[index]
			break
		}
	}

	// 如果没有匹配到具体的反馈类型，给出通用回应
	if responseText == "" {
		responseText = "😊 感谢您的反馈！我会继续努力提供更好的服务。如果您有任何问题或建议，随时可以告诉我。"
		confidence = 0.8
	}

	// 记录用户反馈到学习系统（简化处理）
	if cs.learningEngine != nil {
		logger.Info("📊 记录用户正面反馈到学习系统")
	}

	return &Response{
		Answer:     responseText,
		Source:     "智能反馈处理",
		Intent:     "feedback",
		Confidence: confidence,
		Duration:   time.Since(time.Now()),
	}
}

// handleConfirmation 处理用户确认/同意
func (cs *ChatSystem) handleConfirmation(query string, context *Conversation) *Response {
	logger.Info("✅ 处理用户确认: %s", query)

	queryLower := strings.ToLower(strings.TrimSpace(query))

	// 根据不同的确认类型给出不同的回应
	var responseText string
	var confidence float32 = 0.9

	// 简单确认词（嗯、嗯嗯等）
	simpleConfirmations := []string{"嗯", "嗯嗯", "嗯嗯嗯", "嗯哼", "嗯呢", "嗯啊"}
	for _, word := range simpleConfirmations {
		if queryLower == word {
			responses := []string{
				"😊 好的，我明白了！还有什么我可以帮助您的吗？",
				"👍 收到！如果您有其他问题，随时可以问我。",
				"😄 明白！我随时为您提供帮助。",
				"🤝 好的！有什么需要进一步了解的吗？",
				"✨ 了解！请告诉我还需要什么帮助。",
			}
			// 基于查询内容选择回应
			index := len(query) % len(responses)
			responseText = responses[index]
			break
		}
	}

	// 明确同意词
	explicitConfirmations := []string{"是的", "是", "对", "对的", "没错", "正确", "同意", "认同"}
	for _, word := range explicitConfirmations {
		if queryLower == word {
			responses := []string{
				"👌 很好！我们继续吧，还有什么问题吗？",
				"✅ 太好了！请告诉我您还需要了解什么。",
				"🎯 完全正确！有其他疑问随时问我。",
				"💯 没问题！我随时为您解答。",
			}
			index := len(query) % len(responses)
			responseText = responses[index]
			break
		}
	}

	// 理解确认词
	understandingConfirmations := []string{"好吧", "行吧", "可以", "行", "知道了", "明白了", "了解"}
	for _, word := range understandingConfirmations {
		if queryLower == word {
			responses := []string{
				"😊 很高兴您理解了！有其他需要帮助的地方吗？",
				"👍 太好了！如果还有疑问，请随时告诉我。",
				"🌟 完美！我们可以继续探讨其他话题。",
				"✨ 很棒！还有什么想了解的吗？",
			}
			index := len(query) % len(responses)
			responseText = responses[index]
			break
		}
	}

	// 如果没有匹配到具体的确认类型，给出通用回应
	if responseText == "" {
		responseText = "😊 好的，我理解了！如果您有任何问题或需要进一步的帮助，请随时告诉我。"
		confidence = 0.8
	}

	// 记录用户确认行为到学习系统
	if cs.learningEngine != nil {
		logger.Info("📊 记录用户确认行为到学习系统")
	}

	return &Response{
		Answer:     responseText,
		Source:     "智能确认处理",
		Intent:     "confirmation",
		Confidence: confidence,
		Duration:   time.Since(time.Now()),
	}
}

// applyTopicFilterToVectorResults 对向量搜索结果应用主题检查
func (cs *ChatSystem) applyTopicFilterToVectorResults(query string, results []vectorstore.SearchResult) []vectorstore.SearchResult {
	if len(results) == 0 {
		return results
	}

	logger.Infof("🛡️ 对 %d 个向量搜索结果应用主题检查", len(results))

	var filteredResults []vectorstore.SearchResult
	queryLower := strings.ToLower(query)
	queryTopic := cs.identifyTopic(queryLower)

	for _, result := range results {
		// 获取对应的FAQ
		faq := cs.getFAQByID(result.ID)
		if faq == nil {
			continue
		}

		questionLower := strings.ToLower(faq.Question)
		answerLower := strings.ToLower(faq.Answer)
		questionTopic := cs.identifyTopic(questionLower)
		answerTopic := cs.identifyTopic(answerLower)

		// 使用问题和答案中更明确的主题
		faqTopic := questionTopic
		if answerTopic != "general" && answerTopic != questionTopic {
			faqTopic = answerTopic
		}

		// 检查主题冲突
		if cs.areTopicsConflicting(queryTopic, faqTopic) {
			logger.Infof("🚫 向量搜索结果被主题检查拒绝: %s (主题冲突: %s vs %s)",
				faq.Question, queryTopic, faqTopic)
			continue
		}

		// 通过主题检查，保留结果
		filteredResults = append(filteredResults, result)
	}

	logger.Infof("✅ 主题检查完成: %d/%d 个结果通过", len(filteredResults), len(results))
	return filteredResults
}

// identifyTopic 识别文本主题
func (cs *ChatSystem) identifyTopic(text string) string {
	textLower := strings.ToLower(text)

	// 编程语言主题
	programmingLanguages := map[string]string{
		"go语言": "go", "golang": "go", "go编程": "go", "go开发": "go",
		"python": "python", "python编程": "python", "python开发": "python",
		"java": "java", "java编程": "java", "java开发": "java",
		"javascript": "javascript", "js": "javascript", "前端": "javascript",
		"c++": "cpp", "cpp": "cpp", "c语言": "c",
		"c#": "csharp", "csharp": "csharp", ".net": "csharp",
		"rust": "rust", "php": "php", "ruby": "ruby",
	}

	// 数据库主题
	databases := map[string]string{
		"mysql": "mysql", "postgresql": "postgresql", "postgres": "postgresql",
		"mongodb": "mongodb", "mongo": "mongodb", "redis": "redis",
		"sqlite": "sqlite", "oracle": "oracle", "数据库": "database",
	}

	// 框架和工具主题
	frameworks := map[string]string{
		"react": "react", "vue": "vue", "angular": "angular",
		"django": "django", "flask": "flask", "spring": "spring",
		"express": "express", "nodejs": "nodejs", "node": "nodejs",
		"docker": "docker", "kubernetes": "kubernetes", "k8s": "kubernetes",
	}

	// AI和机器学习主题
	aiTopics := map[string]string{
		"localai": "ai", "人工智能": "ai", "机器学习": "ai", "深度学习": "ai",
		"神经网络": "ai", "ai": "ai", "ml": "ai", "nlp": "ai",
	}

	// 系统部署主题
	deploymentTopics := map[string]string{
		"部署": "deployment", "安装": "deployment", "配置": "deployment",
		"运维": "deployment", "服务器": "deployment", "云服务": "deployment",
		"aws": "deployment", "azure": "deployment", "阿里云": "deployment",
	}

	// 按优先级检查主题
	topicMaps := []map[string]string{
		programmingLanguages, databases, frameworks, aiTopics, deploymentTopics,
	}

	for _, topicMap := range topicMaps {
		for keyword, topic := range topicMap {
			if strings.Contains(textLower, keyword) {
				return topic
			}
		}
	}

	return "general"
}

// areTopicsConflicting 检查两个主题是否冲突
func (cs *ChatSystem) areTopicsConflicting(topic1, topic2 string) bool {
	// 定义冲突的主题组
	conflictGroups := [][]string{
		{"go", "python", "java", "javascript", "cpp", "csharp", "rust", "php", "ruby"}, // 编程语言
		{"mysql", "postgresql", "mongodb", "redis", "sqlite", "oracle"},                // 数据库
		{"react", "vue", "angular"},              // 前端框架
		{"django", "flask", "spring", "express"}, // 后端框架
	}

	for _, group := range conflictGroups {
		found1, found2 := false, false
		for _, item := range group {
			if topic1 == item {
				found1 = true
			}
			if topic2 == item {
				found2 = true
			}
		}
		// 如果两个主题在同一组但不相同，则冲突
		if found1 && found2 && topic1 != topic2 {
			return true
		}
	}

	return false
}

// performNLPAnalysis 执行NLP分析
func (cs *ChatSystem) performNLPAnalysis(query string) (interface{}, error) {
	if cs.nlpProcessor == nil {
		logger.Warnf("NLP处理器未初始化，跳过NLP分析")
		return nil, nil
	}

	logger.Infof("🧠 开始NLP分析: %s", query)

	// 直接调用NLP处理器的ProcessText方法
	result := cs.nlpProcessor.ProcessText(query)
	if result != nil {
		logger.Infof("✅ NLP分析完成")
		return result, nil
	}

	return nil, fmt.Errorf("NLP处理器返回空结果")
}

// applyNLPFilterToVectorResults 使用NLP结果过滤向量搜索结果
func (cs *ChatSystem) applyNLPFilterToVectorResults(query string, results []vectorstore.SearchResult, nlpResult interface{}) []vectorstore.SearchResult {
	if nlpResult == nil {
		logger.Infof("🔄 NLP结果为空，跳过NLP过滤")
		return results
	}

	logger.Infof("🧠 使用NLP结果过滤 %d 个向量搜索结果", len(results))

	var filteredResults []vectorstore.SearchResult

	for _, result := range results {
		// 获取对应的FAQ
		faq := cs.getFAQByID(result.ID)
		if faq == nil {
			continue
		}

		// 使用NLP结果评估FAQ的相关性
		relevanceScore := cs.calculateNLPRelevance(query, faq, nlpResult)

		if relevanceScore > 0.3 { // NLP相关性阈值
			// 调整向量搜索分数
			adjustedResult := result
			adjustedResult.Score = result.Score * float32(relevanceScore)
			filteredResults = append(filteredResults, adjustedResult)

			logger.Infof("✅ NLP过滤通过: %s (相关性: %.3f)", faq.Question, relevanceScore)
		} else {
			logger.Infof("🚫 NLP过滤拒绝: %s (相关性: %.3f)", faq.Question, relevanceScore)
		}
	}

	logger.Infof("✅ NLP过滤完成: %d/%d 个结果通过", len(filteredResults), len(results))
	return filteredResults
}

// calculateNLPRelevance 计算NLP相关性分数
func (cs *ChatSystem) calculateNLPRelevance(query string, faq *mysql.FAQ, nlpResult interface{}) float64 {
	// 基础相关性分数
	baseScore := 0.5

	// 尝试从NLP结果中提取语义信息
	if nlpData, ok := nlpResult.(map[string]interface{}); ok {
		// 检查语义实体匹配
		if entities, exists := nlpData["semantic_entities"]; exists {
			entityScore := cs.calculateEntityRelevance(query, faq, entities)
			baseScore += entityScore * 0.4
		}

		// 检查概念向量相似度
		if concepts, exists := nlpData["concept_vectors"]; exists {
			conceptScore := cs.calculateConceptRelevance(query, faq, concepts)
			baseScore += conceptScore * 0.3
		}

		// 检查语义相似度
		if similarity, exists := nlpData["semantic_similarity"]; exists {
			if simScore, ok := similarity.(float64); ok {
				baseScore += simScore * 0.3
			}
		}
	}

	// 简单的文本相似度作为备选
	textSimilarity := cs.calculateSimpleTextSimilarity(query, faq.Question+" "+faq.Answer)
	baseScore += textSimilarity * 0.2

	// 限制分数范围
	if baseScore > 1.0 {
		baseScore = 1.0
	} else if baseScore < 0.0 {
		baseScore = 0.0
	}

	return baseScore
}

// calculateEntityRelevance 计算实体相关性
func (cs *ChatSystem) calculateEntityRelevance(query string, faq *mysql.FAQ, entities interface{}) float64 {
	// 简化实现：检查实体是否在FAQ中出现
	if entitiesSlice, ok := entities.([]interface{}); ok {
		faqText := strings.ToLower(faq.Question + " " + faq.Answer)
		queryLower := strings.ToLower(query)

		matchCount := 0
		totalEntities := len(entitiesSlice)

		for _, entity := range entitiesSlice {
			if entityMap, ok := entity.(map[string]interface{}); ok {
				if text, ok := entityMap["text"].(string); ok {
					textLower := strings.ToLower(text)
					if strings.Contains(faqText, textLower) || strings.Contains(queryLower, textLower) {
						matchCount++
					}
				}
			}
		}

		if totalEntities > 0 {
			return float64(matchCount) / float64(totalEntities)
		}
	}

	return 0.0
}

// calculateConceptRelevance 计算概念相关性
func (cs *ChatSystem) calculateConceptRelevance(query string, faq *mysql.FAQ, concepts interface{}) float64 {
	// 简化实现：基于概念匹配度
	if conceptsSlice, ok := concepts.([]interface{}); ok {
		if len(conceptsSlice) > 0 {
			return 0.7 // 如果有概念向量，给予中等相关性
		}
	}

	return 0.0
}

// calculateSimpleTextSimilarity 计算简单文本相似度
func (cs *ChatSystem) calculateSimpleTextSimilarity(text1, text2 string) float64 {
	text1Lower := strings.ToLower(text1)
	text2Lower := strings.ToLower(text2)

	// 简单的包含关系检查
	if strings.Contains(text2Lower, text1Lower) {
		return 0.8
	}

	// 词汇重叠度
	words1 := strings.Fields(text1Lower)
	words2 := strings.Fields(text2Lower)

	if len(words1) == 0 || len(words2) == 0 {
		return 0.0
	}

	commonWords := 0
	for _, word1 := range words1 {
		for _, word2 := range words2 {
			if word1 == word2 && len(word1) > 2 { // 忽略过短的词
				commonWords++
				break
			}
		}
	}

	return float64(commonWords) / float64(len(words1))
}

// generateTopicSpecificResponse 根据主题生成针对性的无匹配响应
func (cs *ChatSystem) generateTopicSpecificResponse(query string) string {
	// 识别查询的主题
	topic := cs.identifyQueryTopic(query)

	switch topic {
	case "ai":
		return fmt.Sprintf("🤔 抱歉，我在知识库中没有找到关于「%s」的具体信息。\n\n💡 **针对AI技术问题，建议您：**\n• 尝试搜索相关的AI概念或技术\n• 询问更具体的AI应用场景\n• 了解相关的机器学习基础知识\n\n🔍 **您可能感兴趣的相关话题：**\n• 向量搜索和嵌入技术\n• 自然语言处理(NLP)\n• 机器学习算法原理\n• AI模型部署和应用\n\n🚀 **或者问我其他技术问题：**\n• Go语言开发相关\n• 数据库设计和优化\n• 系统架构和部署", query)

	case "go":
		return fmt.Sprintf("🤔 抱歉，我没有找到关于「%s」的Go语言相关信息。\n\n💡 **针对Go语言问题，建议您：**\n• 询问Go语言的基础语法\n• 了解Go的并发特性\n• 学习Go的标准库使用\n\n🔍 **您可能感兴趣的Go语言话题：**\n• Go语言基础语法和特性\n• Goroutine和Channel并发编程\n• Go Web开发框架\n• Go性能优化技巧\n\n🚀 **或者问我其他技术问题：**\n• 数据库设计和优化\n• 系统架构和部署\n• AI和机器学习技术", query)

	case "python":
		return fmt.Sprintf("🤔 抱歉，我没有找到关于「%s」的Python相关信息。\n\n💡 **针对Python问题，建议您：**\n• 询问Python基础语法\n• 了解Python数据处理\n• 学习Python Web开发\n\n🔍 **您可能感兴趣的Python话题：**\n• Python基础语法和特性\n• 数据科学和机器学习\n• Django/Flask Web开发\n• Python自动化脚本\n\n🚀 **或者问我其他技术问题：**\n• Go语言开发\n• 数据库技术\n• 系统部署", query)

	case "java":
		return fmt.Sprintf("🤔 抱歉，我没有找到关于「%s」的Java相关信息。\n\n💡 **针对Java问题，建议您：**\n• 询问Java基础概念\n• 了解Java企业级开发\n• 学习Spring框架\n\n🔍 **您可能感兴趣的Java话题：**\n• Java基础语法和OOP\n• Spring Boot微服务开发\n• JVM性能调优\n• Java并发编程\n\n🚀 **或者问我其他技术问题：**\n• Go语言开发\n• 数据库技术\n• 前端开发", query)

	case "javascript":
		return fmt.Sprintf("🤔 抱歉，我没有找到关于「%s」的JavaScript相关信息。\n\n💡 **针对JavaScript问题，建议您：**\n• 询问JavaScript基础语法\n• 了解前端框架使用\n• 学习Node.js后端开发\n\n🔍 **您可能感兴趣的JavaScript话题：**\n• JavaScript基础和ES6+特性\n• React/Vue前端开发\n• Node.js后端开发\n• 前端工程化和构建工具\n\n🚀 **或者问我其他技术问题：**\n• 后端开发技术\n• 数据库设计\n• 系统架构", query)

	case "mysql", "database":
		return fmt.Sprintf("🤔 抱歉，我没有找到关于「%s」的数据库相关信息。\n\n💡 **针对数据库问题，建议您：**\n• 询问数据库设计原则\n• 了解SQL查询优化\n• 学习数据库性能调优\n\n🔍 **您可能感兴趣的数据库话题：**\n• MySQL数据库设计和优化\n• SQL查询语句编写\n• 数据库索引和性能\n• 数据库备份和恢复\n\n🚀 **或者问我其他技术问题：**\n• 编程语言开发\n• 系统架构设计\n• AI和机器学习", query)

	case "docker", "kubernetes", "deployment":
		return fmt.Sprintf("🤔 抱歉，我没有找到关于「%s」的部署相关信息。\n\n💡 **针对部署问题，建议您：**\n• 询问容器化部署方案\n• 了解CI/CD流程设计\n• 学习云服务配置\n\n🔍 **您可能感兴趣的部署话题：**\n• Docker容器化技术\n• Kubernetes集群管理\n• 自动化部署流程\n• 云服务器配置\n\n🚀 **或者问我其他技术问题：**\n• 编程语言开发\n• 数据库技术\n• 系统架构设计", query)

	default:
		return fmt.Sprintf("🤔 抱歉，我在FAQ数据库中没有找到关于「%s」的相关信息。\n\n💡 **建议您可以：**\n• 尝试用不同的关键词重新提问\n• 提供更多背景信息或具体场景\n• 将复杂问题拆分成几个简单问题\n\n🔍 **我可以帮您解答的技术领域：**\n• **编程语言**：Go、Python、Java、JavaScript等\n• **数据库技术**：MySQL、Redis、数据库设计等\n• **AI技术**：LocalAI、机器学习、向量搜索等\n• **系统部署**：Docker、Kubernetes、云服务等\n• **Web开发**：前端框架、后端架构等\n\n我会尽力为您提供准确的技术帮助！", query)
	}
}

// identifyQueryTopic 识别查询的主题
func (cs *ChatSystem) identifyQueryTopic(query string) string {
	queryLower := strings.ToLower(query)

	// AI和机器学习主题
	aiKeywords := []string{"localai", "local ai", "人工智能", "机器学习", "深度学习", "神经网络", "ai", "ml", "nlp", "大模型", "llm", "chatgpt", "gpt", "向量", "embedding", "嵌入"}
	for _, keyword := range aiKeywords {
		if strings.Contains(queryLower, keyword) {
			return "ai"
		}
	}

	// 编程语言主题
	if strings.Contains(queryLower, "go语言") || strings.Contains(queryLower, "golang") || strings.Contains(queryLower, "go编程") || strings.Contains(queryLower, "go开发") {
		return "go"
	}
	if strings.Contains(queryLower, "python") || strings.Contains(queryLower, "python编程") || strings.Contains(queryLower, "python开发") {
		return "python"
	}
	if strings.Contains(queryLower, "java") && !strings.Contains(queryLower, "javascript") {
		return "java"
	}
	if strings.Contains(queryLower, "javascript") || strings.Contains(queryLower, "js") || strings.Contains(queryLower, "前端") {
		return "javascript"
	}

	// 数据库主题
	databaseKeywords := []string{"mysql", "postgresql", "mongodb", "redis", "数据库", "sql"}
	for _, keyword := range databaseKeywords {
		if strings.Contains(queryLower, keyword) {
			return "database"
		}
	}

	// 部署主题
	deploymentKeywords := []string{"docker", "kubernetes", "部署", "安装", "配置", "运维", "服务器", "云服务"}
	for _, keyword := range deploymentKeywords {
		if strings.Contains(queryLower, keyword) {
			return "deployment"
		}
	}

	return "general"
}

// isAIRelatedQuery 检查是否是AI相关查询
func (cs *ChatSystem) isAIRelatedQuery(query string) bool {
	aiKeywords := []string{
		"localai", "local ai", "人工智能", "机器学习", "深度学习", "神经网络",
		"ai", "ml", "nlp", "大模型", "llm", "chatgpt", "gpt", "向量", "embedding", "嵌入",
		"算法", "模型", "训练", "预测", "分类", "聚类", "回归", "优化",
	}

	queryLower := strings.ToLower(query)
	for _, keyword := range aiKeywords {
		if strings.Contains(queryLower, keyword) {
			return true
		}
	}

	return false
}

// isAnswerActuallyQuestion 检测答案是否实际上是一个问题
func (cs *ChatSystem) isAnswerActuallyQuestion(answer string) bool {
	answer = strings.TrimSpace(answer)

	// 检查是否以问号结尾
	if strings.HasSuffix(answer, "？") || strings.HasSuffix(answer, "?") {
		// 进一步检查是否包含问题关键词
		questionKeywords := []string{
			"什么是", "如何", "怎么", "为什么", "哪个", "哪些", "谁", "何时", "何地",
			"什么", "怎样", "多少", "几个", "是否", "能否", "可以", "应该",
			"what", "how", "why", "when", "where", "who", "which", "can", "should",
		}

		answerLower := strings.ToLower(answer)
		for _, keyword := range questionKeywords {
			if strings.Contains(answerLower, keyword) {
				return true
			}
		}
	}

	// 检查是否是纯问题格式（即使没有问号）
	answerLower := strings.ToLower(answer)
	questionPatterns := []string{
		"什么是", "如何", "怎么", "为什么", "哪个", "哪些",
		"什么", "怎样", "多少", "几个",
	}

	for _, pattern := range questionPatterns {
		if strings.HasPrefix(answerLower, pattern) {
			// 如果以问题词开头，且长度较短（可能是问题），则认为是问题
			if len(answer) < 100 { // 答案通常比问题长
				return true
			}
		}
	}

	return false
}

// getRelatedKnowledgeRecommendations 获取相关知识点推荐
func (cs *ChatSystem) getRelatedKnowledgeRecommendations(query, answer string) string {
	if cs.recommendationService == nil {
		return ""
	}

	// 创建推荐上下文
	ctx := &recommendation.RecommendationContext{
		Question: query,
		Answer:   answer,
		Source:   "faq",
		Intent:   "technical_question",
	}

	// 1. 首先尝试基于当前问题和答案检测知识点标志
	if cs.knowledgeLearner != nil {
		knowledgeTopic := cs.knowledgeLearner.AutoDetectKnowledgeTopic(query, answer)
		ctx.KnowledgeTopic = knowledgeTopic
	}

	// 2. 使用推荐服务获取推荐
	result := cs.recommendationService.GetRecommendations(ctx)
	if result == nil || !result.Success {
		// 如果推荐服务失败，尝试搜索相关知识
		return cs.getRelatedKnowledgeBySearch(query)
	}

	// 3. 格式化推荐结果
	return cs.recommendationService.FormatRecommendations(result)
}

// getRelatedKnowledgeBySearch 通过搜索获取相关知识
func (cs *ChatSystem) getRelatedKnowledgeBySearch(query string) string {
	if cs.knowledgeLearner == nil {
		return ""
	}

	// 搜索相关的学习知识
	relatedKnowledge, err := cs.knowledgeLearner.SearchLearnedKnowledge(query, 3)
	if err != nil {
		logger.Warnf("搜索相关知识失败: %v", err)
		return ""
	}

	if len(relatedKnowledge) == 0 {
		return ""
	}

	// 过滤掉置信度太低的结果
	var validKnowledge []*learning.LearnedKnowledge
	for _, knowledge := range relatedKnowledge {
		if knowledge.Confidence > 0.3 { // 只显示置信度大于0.3的相关知识
			validKnowledge = append(validKnowledge, knowledge)
		}
	}

	if len(validKnowledge) == 0 {
		return ""
	}

	// 格式化推荐
	var recommendations []string
	for i, knowledge := range validKnowledge {
		if i >= 3 { // 最多显示3个
			break
		}
		recommendations = append(recommendations, fmt.Sprintf("• %s", knowledge.Question))
	}

	return fmt.Sprintf("🔍 **相关问题推荐：**\n%s", strings.Join(recommendations, "\n"))
}

// getRelatedKnowledgeForTopic 根据知识点标志获取相关知识推荐
func (cs *ChatSystem) getRelatedKnowledgeForTopic(knowledgeTopic string, excludeID int) string {
	logger.Infof("🔍 开始获取相关知识: 知识点='%s', 排除ID=%d", knowledgeTopic, excludeID)

	if cs.recommendationService == nil {
		logger.Warnf("❌ 推荐服务未初始化")
		return ""
	}

	if knowledgeTopic == "" {
		logger.Warnf("❌ 知识点标志为空")
		return ""
	}

	// 使用推荐服务获取推荐
	result := cs.recommendationService.GetRecommendationsByTopic(knowledgeTopic, excludeID, 3)
	if result == nil || !result.Success {
		logger.Infof("❌ 推荐服务未返回有效结果")
		return ""
	}

	// 格式化推荐结果
	formattedText := cs.recommendationService.FormatRecommendations(result)
	if formattedText != "" {
		logger.Infof("✅ 推荐服务返回格式化文本，长度: %d", len(formattedText))
	} else {
		logger.Infof("❌ 推荐服务返回空的格式化文本")
	}

	return formattedText
}

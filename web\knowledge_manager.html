<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识管理 - FAQ系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .form-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }

        .form-section h2 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.5em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .form-group textarea {
            min-height: 120px;
            resize: vertical;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .knowledge-list {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            max-height: 80vh;
            overflow-y: auto;
        }

        .knowledge-item {
            background: white;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 8px;
            border-left: 4px solid #4facfe;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .knowledge-item h4 {
            color: #495057;
            margin-bottom: 8px;
            font-size: 1.1em;
        }

        .knowledge-item p {
            color: #6c757d;
            margin-bottom: 8px;
            line-height: 1.5;
        }

        .knowledge-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9em;
            color: #868e96;
            margin-top: 10px;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .status-approved {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .message {
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            font-weight: 500;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4facfe;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #4facfe;
        }

        .stat-label {
            color: #6c757d;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 知识管理中心</h1>
            <p>添加新知识并自动生成向量，提升FAQ系统的智能程度</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalKnowledge">-</div>
                <div class="stat-label">总知识数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="vectorizedKnowledge">-</div>
                <div class="stat-label">已向量化</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="approvedKnowledge">-</div>
                <div class="stat-label">已批准</div>
            </div>
        </div>

        <div class="main-content">
            <div class="form-section">
                <h2>📝 添加新知识</h2>
                
                <form id="knowledgeForm">
                    <div class="form-group">
                        <label for="question">问题 *</label>
                        <input type="text" id="question" name="question" required 
                               placeholder="请输入问题，例如：什么是机器学习？">
                    </div>

                    <div class="form-group">
                        <label for="answer">答案 *</label>
                        <textarea id="answer" name="answer" required 
                                  placeholder="请输入详细的答案..."></textarea>
                    </div>

                    <div class="form-group">
                        <label for="category">分类</label>
                        <select id="category" name="category">
                            <option value="general">通用</option>
                            <option value="technology">技术</option>
                            <option value="business">商业</option>
                            <option value="science">科学</option>
                            <option value="education">教育</option>
                            <option value="health">健康</option>
                            <option value="other">其他</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="keywords">关键词</label>
                        <input type="text" id="keywords" name="keywords" 
                               placeholder="用逗号分隔，例如：机器学习,AI,算法">
                    </div>

                    <div class="form-group">
                        <label for="confidence">置信度</label>
                        <input type="range" id="confidence" name="confidence" 
                               min="0" max="1" step="0.1" value="0.8">
                        <span id="confidenceValue">0.8</span>
                    </div>

                    <button type="submit" class="btn" id="submitBtn">
                        💾 保存知识并生成向量
                    </button>
                </form>

                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>正在保存知识并生成向量...</p>
                </div>

                <div id="message"></div>
            </div>

            <div class="knowledge-list">
                <h2>📚 最近添加的知识</h2>
                <div id="knowledgeList">
                    <p style="text-align: center; color: #6c757d; padding: 20px;">
                        正在加载知识列表...
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
            loadKnowledgeList();
            setupEventListeners();
        });

        // 设置事件监听器
        function setupEventListeners() {
            // 表单提交
            document.getElementById('knowledgeForm').addEventListener('submit', handleSubmit);
            
            // 置信度滑块
            document.getElementById('confidence').addEventListener('input', function() {
                document.getElementById('confidenceValue').textContent = this.value;
            });
        }

        // 处理表单提交
        async function handleSubmit(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const loading = document.getElementById('loading');
            const messageDiv = document.getElementById('message');
            
            // 获取表单数据
            const formData = {
                question: document.getElementById('question').value.trim(),
                answer: document.getElementById('answer').value.trim(),
                category: document.getElementById('category').value,
                keywords: document.getElementById('keywords').value.split(',').map(k => k.trim()).filter(k => k),
                confidence: parseFloat(document.getElementById('confidence').value),
                source: 'user_input',
                learned_from: 'knowledge_manager'
            };

            // 验证数据
            if (!formData.question || !formData.answer) {
                showMessage('请填写问题和答案', 'error');
                return;
            }

            // 显示加载状态
            submitBtn.disabled = true;
            loading.style.display = 'block';
            messageDiv.innerHTML = '';

            try {
                const response = await fetch('/api/learning/knowledge', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    showMessage('✅ 知识保存成功！向量已自动生成', 'success');
                    document.getElementById('knowledgeForm').reset();
                    document.getElementById('confidenceValue').textContent = '0.8';
                    
                    // 刷新列表和统计
                    loadKnowledgeList();
                    loadStatistics();
                } else {
                    showMessage('❌ 保存失败：' + (result.message || '未知错误'), 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showMessage('❌ 网络错误：' + error.message, 'error');
            } finally {
                submitBtn.disabled = false;
                loading.style.display = 'none';
            }
        }

        // 加载统计信息
        async function loadStatistics() {
            try {
                const response = await fetch('/api/learning/statistics');
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('totalKnowledge').textContent = data.data.total || 0;
                    document.getElementById('vectorizedKnowledge').textContent = data.data.vectorized || 0;
                    document.getElementById('approvedKnowledge').textContent = data.data.approved || 0;
                }
            } catch (error) {
                console.error('Failed to load statistics:', error);
            }
        }

        // 加载知识列表
        async function loadKnowledgeList() {
            try {
                const response = await fetch('/api/learning/knowledge?limit=10');
                const data = await response.json();
                
                const listContainer = document.getElementById('knowledgeList');
                
                if (data.success && data.data && data.data.length > 0) {
                    listContainer.innerHTML = data.data.map(item => `
                        <div class="knowledge-item">
                            <h4>${escapeHtml(item.question)}</h4>
                            <p>${escapeHtml(item.answer.substring(0, 150))}${item.answer.length > 150 ? '...' : ''}</p>
                            <div class="knowledge-meta">
                                <span>分类: ${item.category || '未分类'}</span>
                                <span class="status-badge ${item.status === 'approved' ? 'status-approved' : 'status-pending'}">
                                    ${item.status === 'approved' ? '已批准' : '待审核'}
                                </span>
                            </div>
                        </div>
                    `).join('');
                } else {
                    listContainer.innerHTML = '<p style="text-align: center; color: #6c757d; padding: 20px;">暂无知识记录</p>';
                }
            } catch (error) {
                console.error('Failed to load knowledge list:', error);
                document.getElementById('knowledgeList').innerHTML = 
                    '<p style="text-align: center; color: #dc3545; padding: 20px;">加载失败</p>';
            }
        }

        // 显示消息
        function showMessage(message, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="message ${type}">${message}</div>`;
            
            // 3秒后自动隐藏成功消息
            if (type === 'success') {
                setTimeout(() => {
                    messageDiv.innerHTML = '';
                }, 3000);
            }
        }

        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
</body>
</html>

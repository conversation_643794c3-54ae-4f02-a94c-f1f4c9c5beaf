package nlp

import (
	"log"
	"strings"
	"sync"
	"time"
)

// IntegratedProcessor 集成的NLP处理器，结合jieba和spago
type IntegratedProcessor struct {
	jiebaProcessor    *JiebaProcessor
	spagoProcessor    *SpagoProcessor
	intentClassifier  *IntentClassifier
	sentimentAnalyzer *SentimentAnalyzer
	entityExtractor   *EntityExtractor
	contextAnalyzer   *ContextAnalyzer
	relationExtractor *RelationExtractor
	initialized       bool
	mutex             sync.RWMutex
}

// IntegratedResult 集成处理结果
type IntegratedResult struct {
	// 基础分词结果
	Tokens    []string `json:"tokens"`
	Keywords  []string `json:"keywords"`
	Entities  []Entity `json:"entities"`
	Topics    []Topic  `json:"topics"`
	Sentiment string   `json:"sentiment"`

	// 高级NLP结果
	Intent            *IntentResult     `json:"intent,omitempty"`
	SentimentDetail   *SentimentResult  `json:"sentiment_detail,omitempty"`
	ExtractedEntities []ExtractedEntity `json:"extracted_entities"`
	Context           *ContextResult    `json:"context,omitempty"`
	Relations         []Relation        `json:"relations"`

	// Spago高级结果
	SpagoResult *SpagoResult `json:"spago_result,omitempty"`

	// 语义向量分析结果
	SemanticEntities   []SemanticEntity `json:"semantic_entities,omitempty"`
	ConceptVectors     []ConceptVector  `json:"concept_vectors,omitempty"`
	SemanticSimilarity float64          `json:"semantic_similarity,omitempty"`

	// 元数据
	ProcessingTime time.Duration `json:"processing_time"`
	Confidence     float64       `json:"confidence"`
	Method         string        `json:"method"` // "jieba", "spago", "integrated", "enhanced"
	QualityScore   float64       `json:"quality_score"`
}

// SemanticEntity 语义实体
type SemanticEntity struct {
	Text       string            `json:"text"`
	Type       string            `json:"type"`             // technology, person, concept, organization, etc.
	Category   string            `json:"category"`         // programming_language, framework, database, etc.
	Confidence float64           `json:"confidence"`       // 识别置信度
	Context    string            `json:"context"`          // 上下文信息
	Vector     []float32         `json:"vector,omitempty"` // 语义向量
	Attributes map[string]string `json:"attributes"`       // 额外属性
}

// ConceptVector 概念向量
type ConceptVector struct {
	Concept    string    `json:"concept"`    // 概念名称
	Vector     []float32 `json:"vector"`     // 向量表示
	Confidence float64   `json:"confidence"` // 置信度
	Source     string    `json:"source"`     // 来源：jieba, spago, hybrid
}

// NewIntegratedProcessor 创建集成处理器
func NewIntegratedProcessor() *IntegratedProcessor {
	log.Printf("🚀 初始化增强集成NLP处理器 (Jieba + Spago + 高级NLP)")

	processor := &IntegratedProcessor{
		initialized: false,
	}

	// 同步初始化jieba（避免CGO并发问题），其他组件异步初始化
	processor.initializeJiebaSync()

	// 异步初始化其他组件
	go processor.initializeAsync()

	return processor
}

// initializeJiebaSync 同步初始化jieba（避免CGO并发问题）
func (ip *IntegratedProcessor) initializeJiebaSync() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("⚠️ Jieba同步初始化失败: %v", r)
		}
	}()

	log.Printf("🔧 同步初始化Jieba处理器...")
	ip.jiebaProcessor = NewJiebaProcessor()
	log.Printf("   ✅ Jieba处理器同步初始化完成")
}

// initializeAsync 异步初始化
func (ip *IntegratedProcessor) initializeAsync() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("⚠️ 集成处理器初始化失败: %v", r)
			ip.initialized = false
		}
	}()

	log.Printf("🔧 开始初始化增强集成处理器组件...")

	// 1. Jieba处理器已在同步初始化中完成
	log.Printf("   ✅ Jieba处理器已同步初始化")

	// 2. 初始化spago处理器
	ip.spagoProcessor = NewSpagoProcessor()
	log.Printf("   ✅ Spago处理器初始化完成")

	// 3. 初始化高级NLP组件
	ip.intentClassifier = NewIntentClassifier()
	log.Printf("   ✅ 意图分类器初始化完成")

	ip.sentimentAnalyzer = NewSentimentAnalyzer()
	log.Printf("   ✅ 情感分析器初始化完成")

	ip.entityExtractor = NewEntityExtractor()
	log.Printf("   ✅ 实体抽取器初始化完成")

	ip.contextAnalyzer = NewContextAnalyzer()
	log.Printf("   ✅ 上下文分析器初始化完成")

	ip.relationExtractor = NewRelationExtractor()
	log.Printf("   ✅ 关系抽取器初始化完成")

	// 等待所有组件完全初始化
	time.Sleep(time.Second * 3)

	ip.mutex.Lock()
	ip.initialized = true
	ip.mutex.Unlock()

	log.Printf("✅ 增强集成NLP处理器初始化完成")
}

// ProcessText 处理文本
func (ip *IntegratedProcessor) ProcessText(text string) *IntegratedResult {
	startTime := time.Now()

	ip.mutex.RLock()
	isInitialized := ip.initialized
	ip.mutex.RUnlock()

	if !isInitialized {
		log.Printf("⚠️ 增强集成处理器未完全初始化，使用基础处理")
		return ip.basicProcess(text)
	}

	// 检查空文本
	if strings.TrimSpace(text) == "" {
		log.Printf("⚠️ 输入文本为空，返回默认结果")
		return &IntegratedResult{
			Tokens:         []string{},
			Keywords:       []string{},
			Entities:       []Entity{},
			Topics:         []Topic{},
			Sentiment:      "中性",
			Confidence:     0.0,
			Method:         "empty_input",
			ProcessingTime: time.Since(startTime),
		}
	}

	// 安全地截取文本用于日志
	logText := text
	if len(text) > 50 {
		logText = text[:50] + "..."
	}
	log.Printf("🧠 增强集成NLP处理: %s", logText)

	result := &IntegratedResult{
		ProcessingTime: time.Since(startTime),
	}

	// 1. 使用jieba进行基础中文处理
	if ip.jiebaProcessor != nil {
		result.Tokens = ip.jiebaProcessor.SegmentText(text)

		keywords := ip.jiebaProcessor.ExtractKeywords(text, 10)
		result.Keywords = ip.convertKeywordsToStrings(keywords)

		result.Entities = ip.jiebaProcessor.ExtractEntities(text)
		result.Topics = ip.jiebaProcessor.ExtractTopics(text, 5)

		sentiment, _ := ip.jiebaProcessor.AnalyzeSentiment(text)
		result.Sentiment = sentiment

		result.Method = "jieba"
		log.Printf("   ✅ Jieba处理完成: %d tokens, %d keywords", len(result.Tokens), len(result.Keywords))
	}

	// 2. 使用spago进行高级处理
	if ip.spagoProcessor != nil {
		spagoResult := ip.spagoProcessor.ProcessText(text)
		result.SpagoResult = spagoResult

		// 如果jieba没有处理，使用spago的结果
		if result.Tokens == nil || len(result.Tokens) == 0 {
			result.Tokens = spagoResult.Tokens
		}

		// 融合置信度
		if result.Method == "jieba" {
			result.Method = "integrated"
			result.Confidence = (0.6 + spagoResult.Confidence*0.4) // jieba权重0.6，spago权重0.4
		} else {
			result.Method = "spago"
			result.Confidence = spagoResult.Confidence
		}

		log.Printf("   ✅ Spago处理完成: 置信度=%.3f", spagoResult.Confidence)
	}

	// 3. 语义向量分析和智能实体识别
	ip.performSemanticVectorAnalysis(text, result)

	// 4. 执行高级NLP分析
	ip.performAdvancedNLPAnalysis(text, result)

	// 4. 如果都没有处理成功，使用基础处理
	if result.Tokens == nil || len(result.Tokens) == 0 {
		return ip.basicProcess(text)
	}

	result.ProcessingTime = time.Since(startTime)
	log.Printf("✅ 增强集成处理完成: 方法=%s, 置信度=%.3f, 质量=%.3f, 耗时=%v",
		result.Method, result.Confidence, result.QualityScore, result.ProcessingTime)

	return result
}

// basicProcess 基础处理
func (ip *IntegratedProcessor) basicProcess(text string) *IntegratedResult {
	startTime := time.Now()

	result := &IntegratedResult{
		Tokens:            strings.Fields(text),
		Keywords:          []string{},
		Entities:          []Entity{},
		Topics:            []Topic{},
		Sentiment:         "中性",
		ExtractedEntities: []ExtractedEntity{},
		Relations:         []Relation{},
		SpagoResult:       nil,
		ProcessingTime:    time.Millisecond * 5,
		Confidence:        0.3,
		Method:            "basic",
		QualityScore:      0.2,
	}

	// 即使在基础处理中，也尝试执行高级NLP分析
	if ip.initialized {
		ip.performAdvancedNLPAnalysis(text, result)
		result.Method = "basic_enhanced"
		result.ProcessingTime = time.Since(startTime)
	}

	return result
}

// convertKeywordsToStrings 转换关键词为字符串数组
func (ip *IntegratedProcessor) convertKeywordsToStrings(keywords []WordInfo) []string {
	result := make([]string, len(keywords))
	for i, kw := range keywords {
		result[i] = kw.Word
	}
	return result
}

// Close 关闭处理器
func (ip *IntegratedProcessor) Close() {
	if ip.jiebaProcessor != nil {
		ip.jiebaProcessor.Close()
	}
	if ip.spagoProcessor != nil {
		ip.spagoProcessor.Close()
	}
}

// IsInitialized 检查是否已初始化
func (ip *IntegratedProcessor) IsInitialized() bool {
	ip.mutex.RLock()
	defer ip.mutex.RUnlock()
	return ip.initialized
}

// GetProcessorStatus 获取处理器状态
func (ip *IntegratedProcessor) GetProcessorStatus() map[string]interface{} {
	status := map[string]interface{}{
		"initialized":     ip.IsInitialized(),
		"jieba_available": ip.jiebaProcessor != nil && ip.jiebaProcessor.getGlobalJieba() != nil,
		"spago_available": ip.spagoProcessor != nil,
	}

	if ip.spagoProcessor != nil {
		ip.spagoProcessor.mutex.RLock()
		status["spago_initialized"] = ip.spagoProcessor.initialized
		ip.spagoProcessor.mutex.RUnlock()
	}

	return status
}

// performAdvancedNLPAnalysis 执行高级NLP分析
func (ip *IntegratedProcessor) performAdvancedNLPAnalysis(text string, result *IntegratedResult) {
	log.Printf("   🧠 开始高级NLP分析...")

	// 1. 意图识别
	if ip.intentClassifier != nil {
		intentResult := ip.intentClassifier.ClassifyIntent(text)
		result.Intent = &intentResult
		log.Printf("     意图: %s (%.2f)", result.Intent.Intent, result.Intent.Confidence)
	}

	// 2. 详细情感分析
	if ip.sentimentAnalyzer != nil {
		sentimentResult := ip.sentimentAnalyzer.AnalyzeSentiment(text)
		result.SentimentDetail = &sentimentResult
		result.Sentiment = result.SentimentDetail.Label // 保持向后兼容
		log.Printf("     情感: %s (%.2f)", result.SentimentDetail.Label, result.SentimentDetail.Score)
	}

	// 3. 实体抽取
	if ip.entityExtractor != nil {
		result.ExtractedEntities = ip.entityExtractor.ExtractEntities(text)
		log.Printf("     实体: %d 个", len(result.ExtractedEntities))
	}

	// 4. 上下文分析
	if ip.contextAnalyzer != nil {
		var intentForContext IntentResult
		if result.Intent != nil {
			intentForContext = *result.Intent
		}
		contextResult := ip.contextAnalyzer.AnalyzeContext(text, result.Entities, intentForContext)
		result.Context = &contextResult
		log.Printf("     上下文: %s/%s", result.Context.Domain, result.Context.Topic)
	}

	// 5. 关系抽取
	if ip.relationExtractor != nil {
		result.Relations = ip.relationExtractor.ExtractRelations(text, result.Entities)
		log.Printf("     关系: %d 个", len(result.Relations))
	}

	// 6. 计算质量分数
	result.QualityScore = ip.calculateQualityScore(result)

	// 6. 更新处理方法
	if result.Method == "integrated" {
		result.Method = "enhanced"
	} else if result.Method == "jieba" {
		result.Method = "enhanced_jieba"
	} else if result.Method == "spago" {
		result.Method = "enhanced_spago"
	}

	log.Printf("   ✅ 高级NLP分析完成: 质量分数=%.2f", result.QualityScore)
}

// calculateQualityScore 计算质量分数
func (ip *IntegratedProcessor) calculateQualityScore(result *IntegratedResult) float64 {
	score := 0.0

	// 基础分词质量
	if len(result.Tokens) > 0 {
		score += 0.2
	}

	// 关键词提取质量
	if len(result.Keywords) > 0 {
		score += 0.15
	}

	// 实体识别质量
	if len(result.ExtractedEntities) > 0 {
		avgEntityConfidence := 0.0
		for _, entity := range result.ExtractedEntities {
			avgEntityConfidence += entity.Confidence
		}
		avgEntityConfidence /= float64(len(result.ExtractedEntities))
		score += 0.2 * avgEntityConfidence
	}

	// 意图识别质量
	if result.Intent != nil && result.Intent.Confidence > 0 {
		score += 0.15 * result.Intent.Confidence
	}

	// 情感分析质量
	if result.SentimentDetail != nil && result.SentimentDetail.Confidence > 0 {
		score += 0.1 * result.SentimentDetail.Confidence
	}

	// 上下文理解质量
	contextScore := 0.0
	if result.Context != nil {
		if result.Context.Domain != "unknown" {
			contextScore += 0.5
		}
		if result.Context.Topic != "unknown" {
			contextScore += 0.5
		}
	}
	score += 0.08 * contextScore

	// 关系抽取质量
	if len(result.Relations) > 0 {
		avgRelationConfidence := 0.0
		for _, relation := range result.Relations {
			avgRelationConfidence += relation.Confidence
		}
		avgRelationConfidence /= float64(len(result.Relations))
		score += 0.07 * avgRelationConfidence
	}

	// Spago处理质量
	if result.SpagoResult != nil && result.SpagoResult.Confidence > 0 {
		score += 0.1 * result.SpagoResult.Confidence
	}

	return score
}

// performSemanticVectorAnalysis 执行语义向量分析
func (ip *IntegratedProcessor) performSemanticVectorAnalysis(text string, result *IntegratedResult) {
	log.Printf("🔍 执行语义向量分析...")

	// 如果Spago处理器可用，使用其语义向量分析功能
	if ip.spagoProcessor != nil && result.SpagoResult != nil {
		// 从Spago结果中提取语义实体和概念向量
		if len(result.SpagoResult.SemanticEntities) > 0 {
			log.Printf("   提取到 %d 个语义实体", len(result.SpagoResult.SemanticEntities))

			// 将Spago的语义实体转换为集成结果格式
			for _, entity := range result.SpagoResult.SemanticEntities {
				semanticEntity := SemanticEntity{
					Text:       entity.Text,
					Type:       entity.Type,
					Category:   entity.Category,
					Confidence: entity.Confidence,
					Context:    text,
					Vector:     convertFloat64ToFloat32(entity.Vector),
					Attributes: make(map[string]string),
				}

				// 添加属性
				if len(entity.SimilarEntities) > 0 {
					semanticEntity.Attributes["similar_entities"] = strings.Join(entity.SimilarEntities, ",")
				}

				result.SemanticEntities = append(result.SemanticEntities, semanticEntity)
			}
		}

		if len(result.SpagoResult.ConceptVectors) > 0 {
			log.Printf("   提取到 %d 个概念向量", len(result.SpagoResult.ConceptVectors))

			// 将Spago的概念向量转换为集成结果格式
			for _, concept := range result.SpagoResult.ConceptVectors {
				conceptVector := ConceptVector{
					Concept:    concept.Concept,
					Vector:     convertFloat64ToFloat32(concept.Vector),
					Confidence: concept.Confidence,
					Source:     "spago",
				}

				result.ConceptVectors = append(result.ConceptVectors, conceptVector)
			}
		}

		// 计算语义相似度
		if len(result.SpagoResult.SimilarityScores) > 0 {
			maxSimilarity := 0.0
			for _, score := range result.SpagoResult.SimilarityScores {
				if score > maxSimilarity {
					maxSimilarity = score
				}
			}
			result.SemanticSimilarity = maxSimilarity
			log.Printf("   最高语义相似度: %.3f", maxSimilarity)
		}
	} else {
		// 如果Spago不可用，使用基础的语义分析
		ip.performBasicSemanticAnalysis(text, result)
	}

	log.Printf("✅ 语义向量分析完成")
}

// performBasicSemanticAnalysis 执行基础语义分析
func (ip *IntegratedProcessor) performBasicSemanticAnalysis(text string, result *IntegratedResult) {
	log.Printf("   使用基础语义分析...")

	// 基于关键词的简单实体识别
	entities := ip.extractBasicSemanticEntities(text)
	result.SemanticEntities = append(result.SemanticEntities, entities...)

	// 生成基础概念向量
	concepts := ip.generateBasicConceptVectors(text)
	result.ConceptVectors = append(result.ConceptVectors, concepts...)

	result.SemanticSimilarity = 0.5 // 默认相似度
}

// extractBasicSemanticEntities 提取基础语义实体
func (ip *IntegratedProcessor) extractBasicSemanticEntities(text string) []SemanticEntity {
	var entities []SemanticEntity
	textLower := strings.ToLower(text)

	// 预定义的技术实体
	techEntities := map[string]string{
		"go语言":       "programming_language",
		"golang":     "programming_language",
		"python":     "programming_language",
		"java":       "programming_language",
		"javascript": "programming_language",
		"localai":    "ai_framework",
		"mysql":      "database",
		"redis":      "database",
	}

	for entity, category := range techEntities {
		if strings.Contains(textLower, entity) {
			semanticEntity := SemanticEntity{
				Text:       entity,
				Type:       "technology",
				Category:   category,
				Confidence: 0.8,
				Context:    text,
				Vector:     ip.generateBasicVector(entity),
				Attributes: make(map[string]string),
			}
			entities = append(entities, semanticEntity)
		}
	}

	return entities
}

// generateBasicConceptVectors 生成基础概念向量
func (ip *IntegratedProcessor) generateBasicConceptVectors(text string) []ConceptVector {
	var concepts []ConceptVector

	// 基于文本内容生成主要概念
	mainConcept := ip.identifyMainConcept(text)
	if mainConcept != "" {
		conceptVector := ConceptVector{
			Concept:    mainConcept,
			Vector:     ip.generateBasicVector(mainConcept),
			Confidence: 0.7,
			Source:     "basic",
		}
		concepts = append(concepts, conceptVector)
	}

	return concepts
}

// identifyMainConcept 识别主要概念
func (ip *IntegratedProcessor) identifyMainConcept(text string) string {
	textLower := strings.ToLower(text)

	conceptMap := map[string]string{
		"go语言":       "编程语言",
		"golang":     "编程语言",
		"python":     "编程语言",
		"java":       "编程语言",
		"javascript": "编程语言",
		"localai":    "AI框架",
		"mysql":      "数据库",
		"redis":      "数据库",
	}

	for keyword, concept := range conceptMap {
		if strings.Contains(textLower, keyword) {
			return concept
		}
	}

	return "通用概念"
}

// generateBasicVector 生成基础向量
func (ip *IntegratedProcessor) generateBasicVector(text string) []float32 {
	vector := make([]float32, 128)

	// 基于文本哈希生成向量
	hash := 0
	for _, r := range text {
		hash = hash*31 + int(r)
	}

	for i := range vector {
		vector[i] = float32((hash*31+i)%1000) / 1000.0
	}

	return vector
}

// convertFloat64ToFloat32 转换float64切片为float32切片
func convertFloat64ToFloat32(input []float64) []float32 {
	output := make([]float32, len(input))
	for i, v := range input {
		output[i] = float32(v)
	}
	return output
}

package nlp

import (
	"log"
	"regexp"
	"strings"
)

// IntentClassifier 意图分类器
type IntentClassifier struct {
	patterns map[string][]*regexp.Regexp
	keywords map[string][]string
}

// IntentResult 意图识别结果
type IntentResult struct {
	Intent     string                 `json:"intent"`
	Category   string                 `json:"category"`
	Confidence float64                `json:"confidence"`
	Parameters map[string]interface{} `json:"parameters"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// NewIntentClassifier 创建意图分类器
func NewIntentClassifier() *IntentClassifier {
	log.Printf("🎯 初始化意图分类器...")

	ic := &IntentClassifier{
		patterns: make(map[string][]*regexp.Regexp),
		keywords: make(map[string][]string),
	}

	ic.initializePatterns()
	ic.initializeKeywords()

	log.Printf("✅ 意图分类器初始化完成")
	return ic
}

// initializePatterns 初始化模式
func (ic *IntentClassifier) initializePatterns() {
	// 问题询问类
	ic.addPattern("question", `^(什么是|什么叫|如何|怎么|怎样|为什么|为何|哪里|哪个|谁|何时|何地)`)
	ic.addPattern("question", `(是什么|怎么办|如何做|怎么用|怎么样|好不好)`)
	ic.addPattern("question", `.*[？?]$`)

	// 请求帮助类
	ic.addPattern("help", `^(帮我|请帮|能否|可以|麻烦|求助)`)
	ic.addPattern("help", `(需要帮助|寻求帮助|求解|指导)`)

	// 技术查询类
	ic.addPattern("technical", `(API|接口|函数|方法|算法|数据库|服务器|代码|编程|开发)`)
	ic.addPattern("technical", `(配置|部署|安装|调试|错误|异常|bug|性能|优化)`)

	// 信息查找类
	ic.addPattern("search", `^(查找|搜索|寻找|找到|获取|得到)`)
	ic.addPattern("search", `(在哪里|如何找|怎么找|查询|检索)`)

	// 比较评估类
	ic.addPattern("compare", `(比较|对比|区别|差异|优缺点|哪个好|选择)`)
	ic.addPattern("compare", `(vs|versus|和.*比|相比|对比)`)

	// 操作指令类
	ic.addPattern("command", `^(创建|删除|修改|更新|添加|移除|启动|停止|重启)`)
	ic.addPattern("command", `(执行|运行|操作|处理|生成|导出|导入)`)

	// 解释说明类
	ic.addPattern("explain", `(解释|说明|阐述|详细|具体|举例|例子)`)
	ic.addPattern("explain", `(原理|机制|工作方式|实现方法)`)

	// 问题报告类
	ic.addPattern("issue", `(问题|错误|异常|故障|失败|不工作|无法|不能)`)
	ic.addPattern("issue", `(报错|出错|崩溃|卡住|慢|超时)`)

	// 建议咨询类
	ic.addPattern("advice", `(建议|推荐|意见|看法|想法|觉得)`)
	ic.addPattern("advice", `(应该|最好|推荐|建议用|用什么好)`)

	// 闲聊类
	ic.addPattern("chat", `^(你好|hi|hello|嗨|早上好|下午好|晚上好)`)
	ic.addPattern("chat", `(谢谢|感谢|再见|拜拜|bye|thanks)`)

	// 反馈评价类
	ic.addPattern("feedback", `^(不错|很好|好的|棒|赞|厉害|优秀|完美|太好了|好|ok|OK)$`)
	ic.addPattern("feedback", `^(谢谢|感谢|多谢|thanks)$`)
	ic.addPattern("feedback", `^(满意|满足|可以|行|没问题)$`)

	// 问候类
	ic.addPattern("greeting", `^(你好|您好|hi|hello|嗨)$`)
	ic.addPattern("greeting", `^(早上好|下午好|晚上好|晚安)$`)

	// 确认同意类
	ic.addPattern("confirmation", `^(嗯|嗯嗯|嗯嗯嗯|嗯哼|嗯呢|嗯啊)$`)
	ic.addPattern("confirmation", `^(是的|是|对|对的|没错|正确|同意|认同)$`)
	ic.addPattern("confirmation", `^(好吧|行吧|可以|行|知道了|明白了|了解)$`)
	ic.addPattern("confirmation", `^(嗯好|嗯行|嗯对|嗯是|嗯可以)$`)

	// 算法/数学计算类
	ic.addPattern("algorithm_request", `^\d+\s*[\+\-\*\/]\s*\d+\s*=?\s*\??\s*$`) // 1+1, 2*3=?, 等
	ic.addPattern("algorithm_request", `(计算|求|算|等于|多少|结果).*\d+.*[\+\-\*\/].*\d+`)
	ic.addPattern("algorithm_request", `\d+.*[\+\-\*\/].*\d+.*(等于|多少|结果|答案)`)
	ic.addPattern("algorithm_request", `^(计算|求解|求|算).*[\+\-\*\/]`)
	ic.addPattern("algorithm_request", `(数学|算术|运算|计算|求解)`)
	ic.addPattern("algorithm_request", `(加|减|乘|除|平方|开方|幂|次方).*\d+`)
	ic.addPattern("algorithm_request", `\d+.*(加|减|乘|除|平方|开方|幂|次方)`)
}

// initializeKeywords 初始化关键词
func (ic *IntentClassifier) initializeKeywords() {
	ic.keywords["question"] = []string{"什么", "如何", "怎么", "为什么", "哪里", "谁", "何时"}
	ic.keywords["help"] = []string{"帮助", "求助", "指导", "协助", "支持"}
	ic.keywords["technical"] = []string{"技术", "开发", "编程", "代码", "API", "数据库", "服务器"}
	ic.keywords["search"] = []string{"查找", "搜索", "寻找", "获取", "检索"}
	ic.keywords["compare"] = []string{"比较", "对比", "区别", "差异", "选择"}
	ic.keywords["command"] = []string{"创建", "删除", "修改", "执行", "运行", "操作"}
	ic.keywords["explain"] = []string{"解释", "说明", "阐述", "原理", "机制"}
	ic.keywords["issue"] = []string{"问题", "错误", "异常", "故障", "失败"}
	ic.keywords["advice"] = []string{"建议", "推荐", "意见", "应该", "最好"}
	ic.keywords["chat"] = []string{"你好", "谢谢", "再见", "感谢"}
	ic.keywords["feedback"] = []string{"不错", "很好", "好的", "棒", "赞", "厉害", "优秀", "完美", "满意", "好", "ok"}
	ic.keywords["greeting"] = []string{"你好", "您好", "hi", "hello", "嗨", "早上好", "下午好", "晚上好"}
	ic.keywords["confirmation"] = []string{"嗯", "嗯嗯", "是的", "是", "对", "没错", "同意", "好吧", "行", "知道了", "明白了"}
	ic.keywords["algorithm_request"] = []string{"计算", "求", "算", "等于", "多少", "结果", "答案", "数学", "算术", "运算", "加", "减", "乘", "除", "平方", "开方", "幂", "次方"}
}

// addPattern 添加模式
func (ic *IntentClassifier) addPattern(intent string, pattern string) {
	regex, err := regexp.Compile(pattern)
	if err != nil {
		log.Printf("⚠️ 编译正则表达式失败: %s - %v", pattern, err)
		return
	}
	ic.patterns[intent] = append(ic.patterns[intent], regex)
}

// ClassifyIntent 分类意图
func (ic *IntentClassifier) ClassifyIntent(text string) IntentResult {
	text = strings.ToLower(strings.TrimSpace(text))

	if text == "" {
		return IntentResult{
			Intent:     "unknown",
			Category:   "general",
			Confidence: 0.0,
			Parameters: make(map[string]interface{}),
			Metadata:   make(map[string]interface{}),
		}
	}

	// 计算每个意图的得分
	scores := make(map[string]float64)

	// 1. 基于正则模式匹配
	for intent, patterns := range ic.patterns {
		for _, pattern := range patterns {
			if pattern.MatchString(text) {
				scores[intent] += 0.8
			}
		}
	}

	// 2. 基于关键词匹配
	for intent, keywords := range ic.keywords {
		for _, keyword := range keywords {
			if strings.Contains(text, keyword) {
				scores[intent] += 0.3
			}
		}
	}

	// 3. 特殊规则
	ic.applySpecialRules(text, scores)

	// 找到最高得分的意图
	bestIntent := "general"
	bestScore := 0.0

	for intent, score := range scores {
		if score > bestScore {
			bestIntent = intent
			bestScore = score
		}
	}

	// 如果得分太低，归类为一般问题
	if bestScore < 0.3 {
		bestIntent = "general"
		bestScore = 0.5
	}

	// 标准化置信度
	confidence := bestScore
	if confidence > 1.0 {
		confidence = 1.0
	}

	return IntentResult{
		Intent:     bestIntent,
		Category:   ic.getCategory(bestIntent),
		Confidence: confidence,
		Parameters: ic.extractParameters(text, bestIntent),
		Metadata: map[string]interface{}{
			"text_length": len(text),
			"word_count":  len(strings.Fields(text)),
			"all_scores":  scores,
		},
	}
}

// applySpecialRules 应用特殊规则
func (ic *IntentClassifier) applySpecialRules(text string, scores map[string]float64) {
	// 问号强烈表示问题
	if strings.Contains(text, "?") || strings.Contains(text, "？") {
		scores["question"] += 0.5
	}

	// 感叹号可能表示问题或命令
	if strings.Contains(text, "!") || strings.Contains(text, "！") {
		scores["issue"] += 0.2
		scores["command"] += 0.2
	}

	// 长文本更可能是解释请求
	if len(text) > 100 {
		scores["explain"] += 0.2
	}

	// 短文本更可能是简单查询
	if len(text) < 20 {
		scores["search"] += 0.1
	}

	// 包含技术词汇
	techWords := []string{"api", "json", "http", "sql", "html", "css", "js", "python", "go", "java"}
	for _, word := range techWords {
		if strings.Contains(text, word) {
			scores["technical"] += 0.3
			break
		}
	}

	// 数学表达式特殊规则
	if ic.containsMathExpression(text) {
		scores["algorithm_request"] += 1.0 // 高权重
	}

	// 数学表达式特殊规则
	if ic.containsMathExpression(text) {
		scores["algorithm_request"] += 1.0 // 高权重
	}
}

// containsMathExpression 检查是否包含数学表达式
func (ic *IntentClassifier) containsMathExpression(text string) bool {
	text = strings.TrimSpace(text)

	// 检查是否包含数字和运算符的组合
	hasNumber := false
	hasOperator := false

	for _, char := range text {
		if char >= '0' && char <= '9' {
			hasNumber = true
		}
		if strings.ContainsRune("+-*/=×÷", char) {
			hasOperator = true
		}
	}

	// 如果同时包含数字和运算符，很可能是数学表达式
	if hasNumber && hasOperator {
		return true
	}

	// 检查是否包含数学函数
	mathFunctions := []string{"sin", "cos", "tan", "log", "ln", "sqrt", "abs", "exp", "pow"}
	for _, fn := range mathFunctions {
		if strings.Contains(text, fn) {
			return true
		}
	}

	// 检查是否包含明确的数学计算词汇
	mathWords := []string{"计算", "求", "等于", "多少", "结果"}
	for _, word := range mathWords {
		if strings.Contains(text, word) && hasNumber {
			return true
		}
	}

	return false
}

// getCategory 获取分类
func (ic *IntentClassifier) getCategory(intent string) string {
	categories := map[string]string{
		"question":          "inquiry",
		"help":              "support",
		"technical":         "technical",
		"search":            "information",
		"compare":           "analysis",
		"command":           "action",
		"explain":           "education",
		"issue":             "support",
		"advice":            "consultation",
		"chat":              "social",
		"feedback":          "social",
		"greeting":          "social",
		"confirmation":      "social",
		"algorithm_request": "computation",
		"general":           "general",
	}

	if category, exists := categories[intent]; exists {
		return category
	}
	return "general"
}

// extractParameters 提取参数
func (ic *IntentClassifier) extractParameters(text string, intent string) map[string]interface{} {
	params := make(map[string]interface{})

	switch intent {
	case "question":
		params["question_type"] = ic.getQuestionType(text)
	case "technical":
		params["tech_domain"] = ic.getTechDomain(text)
	case "search":
		params["search_target"] = ic.getSearchTarget(text)
	case "compare":
		params["compare_items"] = ic.getCompareItems(text)
	}

	return params
}

// getQuestionType 获取问题类型
func (ic *IntentClassifier) getQuestionType(text string) string {
	if strings.Contains(text, "什么") || strings.Contains(text, "what") {
		return "what"
	} else if strings.Contains(text, "如何") || strings.Contains(text, "怎么") || strings.Contains(text, "how") {
		return "how"
	} else if strings.Contains(text, "为什么") || strings.Contains(text, "why") {
		return "why"
	} else if strings.Contains(text, "哪里") || strings.Contains(text, "where") {
		return "where"
	} else if strings.Contains(text, "何时") || strings.Contains(text, "when") {
		return "when"
	}
	return "general"
}

// getTechDomain 获取技术领域
func (ic *IntentClassifier) getTechDomain(text string) string {
	domains := map[string][]string{
		"web":      {"html", "css", "javascript", "react", "vue", "angular"},
		"backend":  {"api", "server", "database", "sql", "redis", "mongodb"},
		"mobile":   {"android", "ios", "flutter", "react native"},
		"devops":   {"docker", "kubernetes", "ci/cd", "jenkins", "git"},
		"ai":       {"machine learning", "deep learning", "nlp", "ai", "算法"},
		"language": {"python", "java", "go", "c++", "javascript", "php"},
	}

	text = strings.ToLower(text)
	for domain, keywords := range domains {
		for _, keyword := range keywords {
			if strings.Contains(text, keyword) {
				return domain
			}
		}
	}
	return "general"
}

// getSearchTarget 获取搜索目标
func (ic *IntentClassifier) getSearchTarget(text string) string {
	if strings.Contains(text, "文档") || strings.Contains(text, "资料") {
		return "documentation"
	} else if strings.Contains(text, "代码") || strings.Contains(text, "示例") {
		return "code"
	} else if strings.Contains(text, "工具") || strings.Contains(text, "软件") {
		return "tools"
	}
	return "general"
}

// getCompareItems 获取比较项目
func (ic *IntentClassifier) getCompareItems(text string) []string {
	// 简单的比较项目提取
	words := strings.Fields(text)
	var items []string

	for i, word := range words {
		if (word == "和" || word == "与" || word == "vs") && i > 0 && i < len(words)-1 {
			if i > 0 {
				items = append(items, words[i-1])
			}
			if i < len(words)-1 {
				items = append(items, words[i+1])
			}
		}
	}

	return items
}

package humanize

import (
	"faq-system/internal/logger"
	"math/rand"
	"regexp"
	"strings"
	"time"
)

// Processor 人性化处理器
type Processor struct {
	config           *ConversationalConfig
	patterns         []*ConversationalPattern
	emotionalContext *EmotionalContext
	rand             *rand.Rand
}

// NewProcessor 创建人性化处理器
func NewProcessor(config *ConversationalConfig) *Processor {
	if config == nil {
		config = DefaultConversationalConfig()
	}

	processor := &Processor{
		config:   config,
		patterns: loadDefaultPatterns(),
		emotionalContext: &EmotionalContext{
			UserEmotion:      "neutral",
			BotPersonality:   config.DefaultPersonality,
			ConversationTone: "friendly",
			LastInteraction:  time.Now(),
			InteractionCount: 0,
		},
		rand: rand.New(rand.NewSource(time.Now().UnixNano())),
	}

	logger.Infof("🗣️ 人性化处理器已初始化，加载了 %d 个对话模式", len(processor.patterns))
	return processor
}

// ProcessQuery 处理查询
func (p *Processor) ProcessQuery(query string) *HumanizedResponse {
	if !p.config.EnableHumanization {
		return &HumanizedResponse{
			OriginalQuery:  query,
			ShouldContinue: true,
			ProcessedAt:    time.Now(),
		}
	}

	// 更新情感上下文
	p.emotionalContext.LastInteraction = time.Now()
	p.emotionalContext.InteractionCount++

	// 检测是否是口语化表达
	pattern, confidence := p.detectConversationalPattern(query)
	if pattern == nil {
		// 不是口语化表达，继续正常处理
		return &HumanizedResponse{
			OriginalQuery:  query,
			ShouldContinue: true,
			ProcessedAt:    time.Now(),
		}
	}

	// 检查是否是续接请求
	if pattern.Category == "continuation" {
		// 续接请求需要特殊处理，让ChatSystem来处理
		return &HumanizedResponse{
			OriginalQuery:   query,
			DetectedPattern: pattern.Pattern,
			Intent:          pattern.Intent,
			Emotion:         pattern.Emotion,
			Response:        "", // 空回应，让ChatSystem处理
			ResponseType:    "continuation_request",
			Confidence:      confidence,
			ShouldContinue:  true, // 需要继续处理
			ProcessedAt:     time.Now(),
			Metadata: map[string]interface{}{
				"category":          pattern.Category,
				"interaction_count": p.emotionalContext.InteractionCount,
				"is_continuation":   true,
			},
		}
	}

	// 生成人性化回应
	response := p.generateResponse(pattern)

	logger.Infof("🗣️ 检测到口语化表达: '%s' -> 分类='%s', 意图='%s', 置信度=%.2f",
		query, pattern.Category, pattern.Intent, confidence)

	return &HumanizedResponse{
		OriginalQuery:   query,
		DetectedPattern: pattern.Pattern,
		Intent:          pattern.Intent,
		Emotion:         pattern.Emotion,
		Response:        response,
		ResponseType:    "conversational",
		Confidence:      confidence,
		ShouldContinue:  false, // 不需要继续处理
		ProcessedAt:     time.Now(),
		Metadata: map[string]interface{}{
			"category":          pattern.Category,
			"interaction_count": p.emotionalContext.InteractionCount,
		},
	}
}

// ProcessResponse 处理回应
func (p *Processor) ProcessResponse(response string) string {
	if !p.config.EnableHumanization {
		return response
	}

	// 添加人性化元素
	enhancedResponse := p.enhanceResponse(response)
	return enhancedResponse
}

// detectConversationalPattern 检测对话模式
func (p *Processor) detectConversationalPattern(query string) (*ConversationalPattern, float64) {
	normalizedQuery := strings.TrimSpace(strings.ToLower(query))

	// 如果查询太长，可能不是简单的口语化表达
	if len(normalizedQuery) > 15 {
		return nil, 0
	}

	bestMatch := (*ConversationalPattern)(nil)
	bestConfidence := 0.0

	for _, pattern := range p.patterns {
		confidence := p.matchPattern(normalizedQuery, pattern)
		if confidence > bestConfidence && confidence > 0.6 {
			bestMatch = pattern
			bestConfidence = confidence
		}
	}

	return bestMatch, bestConfidence
}

// matchPattern 匹配模式
func (p *Processor) matchPattern(query string, pattern *ConversationalPattern) float64 {
	// 精确匹配
	if query == pattern.Pattern {
		return 1.0
	}

	// 正则表达式匹配
	re, err := regexp.Compile(pattern.Pattern)
	if err == nil && re.MatchString(query) {
		return 0.9
	}

	// 关键词匹配
	for _, keyword := range pattern.Keywords {
		if strings.Contains(query, keyword) {
			return 0.8
		}
	}

	return 0.0
}

// generateResponse 生成回应
func (p *Processor) generateResponse(pattern *ConversationalPattern) string {
	if len(pattern.Responses) == 0 {
		return ""
	}

	// 随机选择一个回应
	index := p.rand.Intn(len(pattern.Responses))
	return pattern.Responses[index]
}

// enhanceResponse 增强回应
func (p *Processor) enhanceResponse(response string) string {
	// 如果回应已经很长，不做修改
	if len(response) > p.config.MaxResponseLength {
		return response
	}

	// 根据对话风格添加人性化元素
	style := p.config.ResponseStyle

	// 添加友好的开头
	if style.Friendly && p.rand.Float64() < 0.3 {
		friendlyIntros := []string{
			"嗯，", "好的，", "这样啊，", "我明白了，", "了解，",
		}
		index := p.rand.Intn(len(friendlyIntros))
		response = friendlyIntros[index] + response
	}

	// 添加共情元素
	if style.Empathetic && p.rand.Float64() < 0.2 {
		empathyPhrases := []string{
			"我理解您的意思，", "从您的角度来看，", "考虑到您的需求，",
		}
		index := p.rand.Intn(len(empathyPhrases))
		if !strings.Contains(response, empathyPhrases[index]) {
			response = empathyPhrases[index] + response
		}
	}

	return response
}

// AddPattern 添加对话模式
func (p *Processor) AddPattern(pattern *ConversationalPattern) {
	p.patterns = append(p.patterns, pattern)
}

// SetEmotionalContext 设置情感上下文
func (p *Processor) SetEmotionalContext(context *EmotionalContext) {
	p.emotionalContext = context
}

// GetEmotionalContext 获取情感上下文
func (p *Processor) GetEmotionalContext() *EmotionalContext {
	return p.emotionalContext
}

// UpdateConfig 更新配置
func (p *Processor) UpdateConfig(config *ConversationalConfig) {
	p.config = config
}

// GetConfig 获取配置
func (p *Processor) GetConfig() *ConversationalConfig {
	return p.config
}

// loadDefaultPatterns 加载默认对话模式
func loadDefaultPatterns() []*ConversationalPattern {
	return []*ConversationalPattern{
		// 确认类
		{
			Pattern:    "哦",
			Category:   "confirmation",
			Intent:     "acknowledge",
			Responses:  []string{"嗯嗯，有什么我可以帮您的吗？", "好的，请问您需要了解什么？", "我在呢，有什么问题尽管问！"},
			Confidence: 0.9,
			Keywords:   []string{"哦"},
			Emotion:    "neutral",
		},
		{
			Pattern:    "哦哦",
			Category:   "confirmation",
			Intent:     "acknowledge",
			Responses:  []string{"是的呢，还有什么想了解的吗？", "嗯嗯，我随时为您服务！", "好的好的，请继续提问吧！"},
			Confidence: 0.9,
			Keywords:   []string{"哦哦"},
			Emotion:    "friendly",
		},
		{
			Pattern:    "嗯",
			Category:   "confirmation",
			Intent:     "acknowledge",
			Responses:  []string{"嗯，有什么需要帮助的吗？", "好的，我在听呢！", "是的，请说！"},
			Confidence: 0.8,
			Keywords:   []string{"嗯"},
			Emotion:    "neutral",
		},
		{
			Pattern:    "嗯嗯",
			Category:   "confirmation",
			Intent:     "acknowledge",
			Responses:  []string{"好的好的，还有什么问题吗？", "嗯嗯，我明白了！", "是的呢，继续吧！"},
			Confidence: 0.9,
			Keywords:   []string{"嗯嗯"},
			Emotion:    "friendly",
		},

		// 反问类
		{
			Pattern:    "不然呢",
			Category:   "rhetorical",
			Intent:     "rhetorical_question",
			Responses:  []string{"哈哈，您说得对！还有其他想了解的吗？", "确实如此呢！有什么其他问题吗？", "您说得很有道理！还需要什么帮助？"},
			Confidence: 0.95,
			Keywords:   []string{"不然呢", "不然"},
			Emotion:    "agreeable",
		},
		{
			Pattern:    "还能怎么样",
			Category:   "rhetorical",
			Intent:     "rhetorical_question",
			Responses:  []string{"是啊，就是这样的！有什么其他需要了解的吗？", "没错呢！还有什么问题吗？", "确实如此！我还能为您做些什么？"},
			Confidence: 0.9,
			Keywords:   []string{"还能怎么样", "怎么样"},
			Emotion:    "agreeable",
		},

		// 肯定类
		{
			Pattern:    "当然啦",
			Category:   "affirmation",
			Intent:     "agreement",
			Responses:  []string{"对呀对呀！还有什么想知道的吗？", "就是这样！有其他问题尽管问！", "没错呢！我很乐意继续帮助您！"},
			Confidence: 0.95,
			Keywords:   []string{"当然啦", "当然"},
			Emotion:    "enthusiastic",
		},
		{
			Pattern:    "对啊",
			Category:   "affirmation",
			Intent:     "agreement",
			Responses:  []string{"是的是的！还有什么需要了解的？", "对呀！有其他问题吗？", "没错！我随时为您服务！"},
			Confidence: 0.9,
			Keywords:   []string{"对啊", "对呀"},
			Emotion:    "agreeable",
		},
		{
			Pattern:    "就是",
			Category:   "affirmation",
			Intent:     "agreement",
			Responses:  []string{"对的对的！还有什么想问的吗？", "就是这样！有其他需要帮助的地方吗？", "没错呢！继续提问吧！"},
			Confidence: 0.8,
			Keywords:   []string{"就是"},
			Emotion:    "agreeable",
		},

		// 疑问类
		{
			Pattern:    "啊？",
			Category:   "confusion",
			Intent:     "clarification",
			Responses:  []string{"有什么不清楚的地方吗？我来详细解释一下！", "需要我再说明一下吗？", "有疑问的话请尽管问，我会耐心解答的！"},
			Confidence: 0.9,
			Keywords:   []string{"啊？", "啊"},
			Emotion:    "confused",
		},
		{
			Pattern:    "什么？",
			Category:   "confusion",
			Intent:     "clarification",
			Responses:  []string{"请问有什么不明白的地方吗？", "需要我重新解释一下吗？", "有什么疑问请告诉我，我来帮您解答！"},
			Confidence: 0.9,
			Keywords:   []string{"什么？"},
			Emotion:    "confused",
		},

		// 感叹类
		{
			Pattern:    "哇",
			Category:   "exclamation",
			Intent:     "surprise",
			Responses:  []string{"是不是很有趣呢？还想了解更多吗？", "对吧！还有什么想知道的？", "很神奇对不对！有其他问题吗？"},
			Confidence: 0.9,
			Keywords:   []string{"哇", "哇塞"},
			Emotion:    "excited",
		},
		{
			Pattern:    "厉害",
			Category:   "praise",
			Intent:     "compliment",
			Responses:  []string{"谢谢夸奖！还有什么我可以帮您的？", "很高兴能帮到您！有其他问题吗？", "您过奖了！还需要了解什么？"},
			Confidence: 0.9,
			Keywords:   []string{"厉害", "牛"},
			Emotion:    "pleased",
		},

		// 续接类
		{
			Pattern:    "请继续",
			Category:   "continuation",
			Intent:     "continue",
			Responses:  []string{"好的，让我继续为您详细说明...", "当然，我来接着讲...", "没问题，继续为您介绍..."},
			Confidence: 0.98,
			Keywords:   []string{"请继续", "继续"},
			Emotion:    "cooperative",
		},
		{
			Pattern:    "继续",
			Category:   "continuation",
			Intent:     "continue",
			Responses:  []string{"好的，接下来...", "继续说...", "那么接着..."},
			Confidence: 0.95,
			Keywords:   []string{"继续"},
			Emotion:    "cooperative",
		},
		{
			Pattern:    "还有呢",
			Category:   "continuation",
			Intent:     "more_info",
			Responses:  []string{"还有很多呢，让我继续告诉您...", "当然还有，接下来...", "是的，还有更多内容..."},
			Confidence: 0.95,
			Keywords:   []string{"还有呢", "还有"},
			Emotion:    "enthusiastic",
		},
		{
			Pattern:    "还有",
			Category:   "continuation",
			Intent:     "more_info",
			Responses:  []string{"还有...", "另外...", "此外..."},
			Confidence: 0.9,
			Keywords:   []string{"还有"},
			Emotion:    "cooperative",
		},
		{
			Pattern:    "然后呢",
			Category:   "continuation",
			Intent:     "continue",
			Responses:  []string{"然后...", "接下来...", "下一步..."},
			Confidence: 0.95,
			Keywords:   []string{"然后呢", "然后"},
			Emotion:    "curious",
		},
		{
			Pattern:    "接下来",
			Category:   "continuation",
			Intent:     "continue",
			Responses:  []string{"接下来...", "下面...", "然后..."},
			Confidence: 0.9,
			Keywords:   []string{"接下来"},
			Emotion:    "cooperative",
		},
		{
			Pattern:    "下面呢",
			Category:   "continuation",
			Intent:     "continue",
			Responses:  []string{"下面...", "接着...", "然后..."},
			Confidence: 0.9,
			Keywords:   []string{"下面呢", "下面"},
			Emotion:    "curious",
		},

		// 礼貌用语
		{
			Pattern:    "谢谢",
			Category:   "gratitude",
			Intent:     "thanks",
			Responses:  []string{"不客气！很高兴能帮到您！", "不用谢！还有什么需要帮助的吗？", "我的荣幸！有其他问题随时问我！"},
			Confidence: 0.95,
			Keywords:   []string{"谢谢", "感谢"},
			Emotion:    "grateful",
		},
		{
			Pattern:    "再见",
			Category:   "farewell",
			Intent:     "goodbye",
			Responses:  []string{"再见！有问题随时来找我哦！", "拜拜！期待下次为您服务！", "再见！祝您一切顺利！"},
			Confidence: 0.95,
			Keywords:   []string{"再见", "拜拜", "88"},
			Emotion:    "friendly",
		},
	}
}

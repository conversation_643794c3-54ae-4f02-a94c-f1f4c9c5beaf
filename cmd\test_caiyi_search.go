package main

import (
	"database/sql"
	"fmt"
	"log"
	"strings"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 连接数据库
	dsn := "root:123456@tcp(127.0.0.1:33508)/faqdb?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}
	defer db.Close()

	// 测试连接
	if err := db.<PERSON>(); err != nil {
		log.Fatal("数据库连接测试失败:", err)
	}

	fmt.Println("🔍 开始搜索蔡依林相关数据...")

	// 1. 搜索包含"蔡依林"的问题
	fmt.Println("\n=== 搜索问题中包含'蔡依林'的记录 ===")
	searchInColumn(db, "question", "蔡依林")

	// 2. 搜索包含"蔡依林"的答案
	fmt.Println("\n=== 搜索答案中包含'蔡依林'的记录 ===")
	searchInColumn(db, "answer", "蔡依林")

	// 3. 搜索包含"蔡"的记录
	fmt.Println("\n=== 搜索包含'蔡'字的记录 ===")
	searchInColumn(db, "question", "蔡")

	// 4. 显示最新的20条记录
	fmt.Println("\n=== 显示最新的20条学习知识记录 ===")
	showLatestRecords(db, 20)

	// 5. 统计总记录数
	fmt.Println("\n=== 统计信息 ===")
	showStatistics(db)
}

func searchInColumn(db *sql.DB, column, keyword string) {
	query := fmt.Sprintf(`
		SELECT id, question, answer, created_at, status 
		FROM learned_knowledge 
		WHERE %s LIKE ? 
		ORDER BY created_at DESC 
		LIMIT 10
	`, column)

	rows, err := db.Query(query, "%"+keyword+"%")
	if err != nil {
		log.Printf("查询失败: %v", err)
		return
	}
	defer rows.Close()

	count := 0
	for rows.Next() {
		var id int
		var question, answer, status string
		var createdAt string

		err := rows.Scan(&id, &question, &answer, &createdAt, &status)
		if err != nil {
			log.Printf("扫描行失败: %v", err)
			continue
		}

		count++
		fmt.Printf("ID: %d | 状态: %s | 时间: %s\n", id, status, createdAt)
		fmt.Printf("问题: %s\n", truncateString(question, 100))
		fmt.Printf("答案: %s\n", truncateString(answer, 100))
		fmt.Println(strings.Repeat("-", 80))
	}

	if count == 0 {
		fmt.Printf("❌ 在%s列中未找到包含'%s'的记录\n", column, keyword)
	} else {
		fmt.Printf("✅ 在%s列中找到%d条包含'%s'的记录\n", column, count, keyword)
	}
}

func showLatestRecords(db *sql.DB, limit int) {
	query := `
		SELECT id, question, answer, created_at, status 
		FROM learned_knowledge 
		ORDER BY created_at DESC 
		LIMIT ?
	`

	rows, err := db.Query(query, limit)
	if err != nil {
		log.Printf("查询最新记录失败: %v", err)
		return
	}
	defer rows.Close()

	count := 0
	for rows.Next() {
		var id int
		var question, answer, status string
		var createdAt string

		err := rows.Scan(&id, &question, &answer, &createdAt, &status)
		if err != nil {
			log.Printf("扫描行失败: %v", err)
			continue
		}

		count++
		fmt.Printf("%d. ID: %d | 状态: %s | 时间: %s\n", count, id, status, createdAt)
		fmt.Printf("   问题: %s\n", truncateString(question, 80))
		fmt.Printf("   答案: %s\n", truncateString(answer, 80))
		fmt.Println()
	}

	fmt.Printf("显示了最新的%d条记录\n", count)
}

func showStatistics(db *sql.DB) {
	// 总记录数
	var total int
	err := db.QueryRow("SELECT COUNT(*) FROM learned_knowledge").Scan(&total)
	if err != nil {
		log.Printf("查询总记录数失败: %v", err)
	} else {
		fmt.Printf("📊 总记录数: %d\n", total)
	}

	// 按状态统计
	statusQuery := `
		SELECT status, COUNT(*) 
		FROM learned_knowledge 
		GROUP BY status
	`
	rows, err := db.Query(statusQuery)
	if err != nil {
		log.Printf("查询状态统计失败: %v", err)
		return
	}
	defer rows.Close()

	fmt.Println("📊 按状态统计:")
	for rows.Next() {
		var status string
		var count int
		err := rows.Scan(&status, &count)
		if err != nil {
			continue
		}
		fmt.Printf("   %s: %d条\n", status, count)
	}
}

func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}
